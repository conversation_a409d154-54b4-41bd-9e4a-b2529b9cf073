# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [3.2.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@3.2.0) (2020-11-05)


### Bug Fixes

* ignore gpg lines ([#685](https://github.com/conventional-changelog/conventional-changelog/issues/685)) ([f8fcbc2](https://github.com/conventional-changelog/conventional-changelog/commit/f8fcbc2e8b0834c29178ace6382b438a020ad828))
* **deps:** update dependency through2 to v4 ([#657](https://github.com/conventional-changelog/conventional-changelog/issues/657)) ([7ae618c](https://github.com/conventional-changelog/conventional-changelog/commit/7ae618c81491841e5b1d796d3933aac0c54bc312))


### Features

* allows notes pattern to be customized ([#586](https://github.com/conventional-changelog/conventional-changelog/issues/586)) ([9c00f32](https://github.com/conventional-changelog/conventional-changelog/commit/9c00f3242d916be1774a618d943f908f8d9699a6))





# [3.1.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@3.1.0) (2020-05-08)


### Bug Fixes

* **deps:** update yargs-parser to move off a flagged-vulnerable version. ([#635](https://github.com/conventional-changelog/conventional-changelog/issues/635)) ([aafc0f0](https://github.com/conventional-changelog/conventional-changelog/commit/aafc0f00412c3e4b23b8418300e5a570a48fe24d))


### Features

* **conventional-commits-parser:** add issuePrefixesCaseSensitive parser option ([#580](https://github.com/conventional-changelog/conventional-changelog/issues/580)) ([526b282](https://github.com/conventional-changelog/conventional-changelog/commit/526b28214d12c55158eb2e4d44408378587ceb97))
* support slash in headerPattern default options ([93a547d](https://github.com/conventional-changelog/conventional-changelog/commit/93a547d742634d8676f499cfa2a274bc3792d020))





### [3.2.4](https://github.com/conventional-changelog/conventional-changelog/compare/conventional-commits-parser-v3.2.3...conventional-commits-parser-v3.2.4) (2021-12-29)


### Bug Fixes

* support BREAKING-CHANGE alongside BREAKING CHANGE ([#882](https://github.com/conventional-changelog/conventional-changelog/issues/882)) ([e6f44ad](https://github.com/conventional-changelog/conventional-changelog/commit/e6f44adcf1ac5abbb85bdac73237c331c6594177))

### [3.2.3](https://www.github.com/conventional-changelog/conventional-changelog/compare/conventional-commits-parser-v3.2.2...conventional-commits-parser-v3.2.3) (2021-10-23)


### Bug Fixes

* address ReDoS issue ([#861](https://www.github.com/conventional-changelog/conventional-changelog/issues/861)) ([c696fa3](https://www.github.com/conventional-changelog/conventional-changelog/commit/c696fa35f93e0ee13728d6cf1221587ac6386311))

### [3.2.2](https://www.github.com/conventional-changelog/conventional-changelog/compare/conventional-commits-parser-v3.2.1...conventional-commits-parser-v3.2.2) (2021-09-09)


### Bug Fixes

* **conventional-commits-parser:** address CVE-2021-23425 ([#841](https://www.github.com/conventional-changelog/conventional-changelog/issues/841)) ([02b3d53](https://www.github.com/conventional-changelog/conventional-changelog/commit/02b3d53a0c142f0c28ee7d190d210c76a62887c2))

### [3.2.1](https://www.github.com/conventional-changelog/conventional-changelog/compare/conventional-commits-parser@3.2.0...v3.2.1) (2021-02-15)


### Bug Fixes

* handle missing header in merge commit ([#757](https://www.github.com/conventional-changelog/conventional-changelog/issues/757)) ([d189d3e](https://www.github.com/conventional-changelog/conventional-changelog/commit/d189d3e45b82e7141115ce8eccd95c8cf2d7db77))

## [3.0.8](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@3.0.8) (2019-11-14)


### Bug Fixes

* add types for cli flags ([#551](https://github.com/conventional-changelog/conventional-changelog/issues/551)) ([bf1d64a](https://github.com/conventional-changelog/conventional-changelog/commit/bf1d64aeaf8f262d4b2beec02d2aebb78df7343b))





## [3.0.7](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@3.0.7) (2019-11-07)


### Bug Fixes

* **conventional-commits-parser:** add breaking change notes if header match `breakingHeaderPattern` ([#544](https://github.com/conventional-changelog/conventional-changelog/issues/544)) ([efdf3cb](https://github.com/conventional-changelog/conventional-changelog/commit/efdf3cbc9de3278b180a48beebb74e596e3c5f94))
* **conventional-commits-parser:** add missing separator pipe to non tty parser ([#546](https://github.com/conventional-changelog/conventional-changelog/issues/546)) ([c522743](https://github.com/conventional-changelog/conventional-changelog/commit/c5227437b0b300f30a57e8ba5df2a8ab8d163af0))





## [3.0.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@3.0.6) (2019-10-24)


### Bug Fixes

* **conventional-commits-parser:** downgrade is-text-path due to node 6 incompatibility ([#536](https://github.com/conventional-changelog/conventional-changelog/issues/536)) ([3aa2637](https://github.com/conventional-changelog/conventional-changelog/commit/3aa2637a1c65bb4db3d8bf2c6ce17e6f5abe1ca1))
* **deps:** update lodash to fix security issues ([#535](https://github.com/conventional-changelog/conventional-changelog/issues/535)) ([ac43f51](https://github.com/conventional-changelog/conventional-changelog/commit/ac43f51de1f3b597c32f7f8442917a2d06199018))





## [3.0.4](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@3.0.4) (2019-10-02)

**Note:** Version bump only for package conventional-commits-parser





## [3.0.3](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@3.0.3) (2019-05-18)


### Bug Fixes

* **deps:** update dependency is-text-path to v2 ([#455](https://github.com/conventional-changelog/conventional-changelog/issues/455)) ([0f40ec3](https://github.com/conventional-changelog/conventional-changelog/commit/0f40ec3))





## [3.0.2](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@3.0.2) (2019-04-10)


### Bug Fixes

* **deps:** update dependency through2 to v3 ([#392](https://github.com/conventional-changelog/conventional-changelog/issues/392)) ([26fe91f](https://github.com/conventional-changelog/conventional-changelog/commit/26fe91f))





## [3.0.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@3.0.1) (2018-11-01)


### Bug Fixes

* Upgrade to Lerna 3, fix Node.js v11 error ([#385](https://github.com/conventional-changelog/conventional-changelog/issues/385)) ([cdef282](https://github.com/conventional-changelog/conventional-changelog/commit/cdef282))





      <a name="3.0.0"></a>
# [3.0.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@3.0.0) (2018-05-29)


### Chores

* **package:** set Node requirement to oldest supported LTS ([#329](https://github.com/conventional-changelog/conventional-changelog/issues/329)) ([cae2fe0](https://github.com/conventional-changelog/conventional-changelog/commit/cae2fe0))


### BREAKING CHANGES

* **package:** Set the package's minimum required Node version to be the oldest LTS
currently supported by the Node Release working group. At this time,
that is Node 6 (which is in its Maintenance LTS phase).




      <a name="2.1.7"></a>
## [2.1.7](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@2.1.7) (2018-03-27)




**Note:** Version bump only for package conventional-commits-parser

<a name="2.1.6"></a>
## [2.1.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@2.1.6) (2018-03-22)




**Note:** Version bump only for package conventional-commits-parser

<a name="2.1.5"></a>
## [2.1.5](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@2.1.5) (2018-02-24)




**Note:** Version bump only for package conventional-commits-parser

<a name="2.1.4"></a>
## [2.1.4](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-parser@2.1.4) (2018-02-20)




**Note:** Version bump only for package conventional-commits-parser

<a name="2.1.3"></a>
## [2.1.3](https://github.com/conventional-changelog/conventional-commits-parser/compare/<EMAIL>-commits-parser@2.1.3) (2018-02-13)




**Note:** Version bump only for package conventional-commits-parser

<a name="2.1.2"></a>
## [2.1.2](https://github.com/conventional-changelog/conventional-commits-parser/compare/<EMAIL>-commits-parser@2.1.2) (2018-02-13)




**Note:** Version bump only for package conventional-commits-parser

<a name="2.1.1"></a>
## [2.1.1](https://github.com/conventional-changelog/conventional-commits-parser/compare/<EMAIL>-commits-parser@2.1.1) (2018-02-05)


### Bug Fixes

* truncate after scissors line ([#267](https://github.com/conventional-changelog/conventional-commits-parser/issues/267)) ([e09df10](https://github.com/conventional-changelog/conventional-commits-parser/commit/e09df10))




<a name="2.1.0"></a>
# [2.1.0](https://github.com/conventional-changelog/conventional-commits-parser/compare/<EMAIL>-commits-parser@2.1.0) (2017-12-08)


### Bug Fixes

* always parse references ([e84a9ae](https://github.com/conventional-changelog/conventional-commits-parser/commit/e84a9ae)), closes [#248](https://github.com/conventional-changelog/conventional-commits-parser/issues/248)


### Features

* make comment stripping optional ([db5b711](https://github.com/conventional-changelog/conventional-commits-parser/commit/db5b711)), closes [#251](https://github.com/conventional-changelog/conventional-commits-parser/issues/251)




<a name="2.0.1"></a>
## [2.0.1](https://github.com/conventional-changelog/conventional-commits-parser/compare/<EMAIL>-commits-parser@2.0.1) (2017-11-13)


### Bug Fixes

* **conventional-commits-parser:** ignore comments  ([#231](https://github.com/conventional-changelog/conventional-commits-parser/issues/231)) ([9db53e3](https://github.com/conventional-changelog/conventional-commits-parser/commit/9db53e3)), closes [#224](https://github.com/conventional-changelog/conventional-commits-parser/issues/224)




<a name="2.0.0"></a>
# 2.0.0 (2017-07-17)


### Bug Fixes

* **cli:** commit can be split when testing a commit file ([f3b3a3f](https://github.com/conventional-changelog/conventional-commits-parser/commit/f3b3a3f))
* **cli:** error handling for ENOENT is fixed ([3c92233](https://github.com/conventional-changelog/conventional-commits-parser/commit/3c92233))
* **cli:** fix "undefined" in json string ([0680e42](https://github.com/conventional-changelog/conventional-commits-parser/commit/0680e42))
* **cli:** options format ([491357e](https://github.com/conventional-changelog/conventional-commits-parser/commit/491357e))
* **deps:** require split2 ([1941c37](https://github.com/conventional-changelog/conventional-commits-parser/commit/1941c37))
* **error:** change error type and wordings ([d8be5e5](https://github.com/conventional-changelog/conventional-commits-parser/commit/d8be5e5))
* **footer:** notes contains more than one paragraphs after references ([d744ec7](https://github.com/conventional-changelog/conventional-commits-parser/commit/d744ec7))
* **headerCorrespondence:** string value for cli ([fb774fc](https://github.com/conventional-changelog/conventional-commits-parser/commit/fb774fc))
* **headerPattern:** change how capturing groups works ([fe1fe0c](https://github.com/conventional-changelog/conventional-commits-parser/commit/fe1fe0c))
* **issuePrefixes:** should return noMatch if falsy ([72db2bf](https://github.com/conventional-changelog/conventional-commits-parser/commit/72db2bf))
* **mention:** fix mention matching ([965986b](https://github.com/conventional-changelog/conventional-commits-parser/commit/965986b)), closes [#26](https://github.com/conventional-changelog/conventional-commits-parser/issues/26)
* **newlines:** preserve newlines in a part ([06b8c7c](https://github.com/conventional-changelog/conventional-commits-parser/commit/06b8c7c)), closes [#15](https://github.com/conventional-changelog/conventional-commits-parser/issues/15)
* **notes:** note keywords must appear at the beginning of a sentence ([5a2059e](https://github.com/conventional-changelog/conventional-commits-parser/commit/5a2059e)), closes [#23](https://github.com/conventional-changelog/conventional-commits-parser/issues/23)
* **parser:** do not trim spaces but newlines ([1e8c4c5](https://github.com/conventional-changelog/conventional-commits-parser/commit/1e8c4c5))
* **parser:** it returns null if there is no header ([8571c9e](https://github.com/conventional-changelog/conventional-commits-parser/commit/8571c9e))
* **regex:** do not treat it as note if there are texts after keywords ([571b03e](https://github.com/conventional-changelog/conventional-commits-parser/commit/571b03e))
* **regex:** make getReferencePartsRegex stricter ([62adf54](https://github.com/conventional-changelog/conventional-commits-parser/commit/62adf54)), closes [#27](https://github.com/conventional-changelog/conventional-commits-parser/issues/27) [#30](https://github.com/conventional-changelog/conventional-commits-parser/issues/30) [#27](https://github.com/conventional-changelog/conventional-commits-parser/issues/27) [#28](https://github.com/conventional-changelog/conventional-commits-parser/issues/28)
* **revertPattern:** correct regex ([8628983](https://github.com/conventional-changelog/conventional-commits-parser/commit/8628983))
* **util:** remove an accidentally commited file ([3710a8c](https://github.com/conventional-changelog/conventional-commits-parser/commit/3710a8c))
* **warn:** should tell which commit cannot be parsed ([04b0a9b](https://github.com/conventional-changelog/conventional-commits-parser/commit/04b0a9b))


### Chores

* init ([a529841](https://github.com/conventional-changelog/conventional-commits-parser/commit/a529841))


### Code Refactoring

* **breaks:** change `breaks` to `notes` ([5189a61](https://github.com/conventional-changelog/conventional-commits-parser/commit/5189a61)), closes [#2](https://github.com/conventional-changelog/conventional-commits-parser/issues/2)
* **merge:** pull-request should be merge ([4e7c61c](https://github.com/conventional-changelog/conventional-commits-parser/commit/4e7c61c))
* **regex:** regex now takes `options` ([eea319a](https://github.com/conventional-changelog/conventional-commits-parser/commit/eea319a))


### Features

* **cli:** able to have two files, separator works for interactive ([db1e3b5](https://github.com/conventional-changelog/conventional-commits-parser/commit/db1e3b5))
* **cli:** add aliases, more help details and tests ([eb654a2](https://github.com/conventional-changelog/conventional-commits-parser/commit/eb654a2))
* **cli:** add missing options ([8ac1cf7](https://github.com/conventional-changelog/conventional-commits-parser/commit/8ac1cf7))
* **cli:** add warn function for interactive shell ([84fe31f](https://github.com/conventional-changelog/conventional-commits-parser/commit/84fe31f))
* **correspondence:** add `headerCorrespondence` and improve commit parts ([aca9e95](https://github.com/conventional-changelog/conventional-commits-parser/commit/aca9e95)), closes [#6](https://github.com/conventional-changelog/conventional-commits-parser/issues/6)
* **fieldPattern:** should support string format ([b6b6b52](https://github.com/conventional-changelog/conventional-commits-parser/commit/b6b6b52))
* **hash:** drop support ([1ccc751](https://github.com/conventional-changelog/conventional-commits-parser/commit/1ccc751))
* **headerParts:** headerParts can be anything ([31e1c11](https://github.com/conventional-changelog/conventional-commits-parser/commit/31e1c11)), closes [#10](https://github.com/conventional-changelog/conventional-commits-parser/issues/10)
* **issuePrefixes:** init and referenceKeywords -> referenceActions ([86bf798](https://github.com/conventional-changelog/conventional-commits-parser/commit/86bf798)), closes [#11](https://github.com/conventional-changelog/conventional-commits-parser/issues/11)
* **maxSubjectLength:** removed ([3448582](https://github.com/conventional-changelog/conventional-commits-parser/commit/3448582))
* **mentions:** [@someone](https://github.com/someone) in commit ([d60fe76](https://github.com/conventional-changelog/conventional-commits-parser/commit/d60fe76)), closes [#24](https://github.com/conventional-changelog/conventional-commits-parser/issues/24)
* **newline:** fields does not contain leading or trailing newlines ([6db453b](https://github.com/conventional-changelog/conventional-commits-parser/commit/6db453b)), closes [#14](https://github.com/conventional-changelog/conventional-commits-parser/issues/14)
* **note:** noteKeywords is case insensitive ([f779a29](https://github.com/conventional-changelog/conventional-commits-parser/commit/f779a29)), closes [#21](https://github.com/conventional-changelog/conventional-commits-parser/issues/21)
* **options:** all options can be a string ([0fa17b4](https://github.com/conventional-changelog/conventional-commits-parser/commit/0fa17b4))
* **otherFields:** other fields are possible to be included ([9e06278](https://github.com/conventional-changelog/conventional-commits-parser/commit/9e06278))
* improvements and bug fixes ([1cde104](https://github.com/conventional-changelog/conventional-commits-parser/commit/1cde104)), closes [#5](https://github.com/conventional-changelog/conventional-commits-parser/issues/5)
* migrate repo to lerna mono-repo ([793e823](https://github.com/conventional-changelog/conventional-commits-parser/commit/793e823))
* **regex:** matching JIRA-123 like references ([20f1f7a](https://github.com/conventional-changelog/conventional-commits-parser/commit/20f1f7a)), closes [#19](https://github.com/conventional-changelog/conventional-commits-parser/issues/19)
* support squash commits ([#31](https://github.com/conventional-changelog/conventional-changelog/issues/31)) ([fff60c0](https://github.com/conventional-changelog/conventional-commits-parser/commit/fff60c0))
* **owner:** yield owner field ([d8d0334](https://github.com/conventional-changelog/conventional-commits-parser/commit/d8d0334)), closes [#12](https://github.com/conventional-changelog/conventional-commits-parser/issues/12)
* **parser:** notes can have more than one paragraph ([733bfa9](https://github.com/conventional-changelog/conventional-commits-parser/commit/733bfa9)), closes [#3](https://github.com/conventional-changelog/conventional-commits-parser/issues/3)
* **pullRequest:** Allow to skip and parse pull request header ([a2e929f](https://github.com/conventional-changelog/conventional-commits-parser/commit/a2e929f)), closes [#20](https://github.com/conventional-changelog/conventional-commits-parser/issues/20)
* **reference:** able to reference an issue without an action ([6474123](https://github.com/conventional-changelog/conventional-commits-parser/commit/6474123)), closes [#22](https://github.com/conventional-changelog/conventional-commits-parser/issues/22)
* **reference:** expose prefix ([47df766](https://github.com/conventional-changelog/conventional-commits-parser/commit/47df766)), closes [#17](https://github.com/conventional-changelog/conventional-commits-parser/issues/17)
* **references:** allow header to reference an issue ([df18a24](https://github.com/conventional-changelog/conventional-commits-parser/commit/df18a24))
* **references:** support other formats of references ([7c70213](https://github.com/conventional-changelog/conventional-commits-parser/commit/7c70213)), closes [#4](https://github.com/conventional-changelog/conventional-commits-parser/issues/4) [#8](https://github.com/conventional-changelog/conventional-commits-parser/issues/8)
* **regex:** leading and trailing space for closes and breaks keywords are trimmed ([9639860](https://github.com/conventional-changelog/conventional-commits-parser/commit/9639860))
* **revert:** parse a commit that reverts ([2af7233](https://github.com/conventional-changelog/conventional-commits-parser/commit/2af7233))
* **stream:** emmit an empty string to down stream if commit cannot be parsed ([76bf84e](https://github.com/conventional-changelog/conventional-commits-parser/commit/76bf84e))
* **sync:** add the sync function ([82071c6](https://github.com/conventional-changelog/conventional-commits-parser/commit/82071c6)), closes [#13](https://github.com/conventional-changelog/conventional-commits-parser/issues/13)
* **warn:** optionally warn user what is wrong when commit cannot be parsed ([32b3cda](https://github.com/conventional-changelog/conventional-commits-parser/commit/32b3cda))


### Performance Improvements

* **regex:** regex should be constructed in index.html ([15afd26](https://github.com/conventional-changelog/conventional-commits-parser/commit/15afd26))


### BREAKING CHANGES

* **merge:** `pull request` should be `merge`. Also make the parsed result to be consistent with other parts.
* This module is imported from https://github.com/ajoslin/conventional-changelog, and is originally written by @vojtajina, @btford and @ajoslin.
* **hash:** hash is no longer supported. This parser should just parse raw commit messages. Also text fields are appended with a newline "
".
* **regex:** It returns a nomatch regex if it's keywords are missing.
* **headerParts:** `headerParts` does not limit to `type`, `scope` and `subject`. They can now be defined in `options.headerCorrespondence` and the order is the order of capturing group in `options.headerPattern`. If part is missing in `options.headerCorrespondence` it is `undefined`. If part is not captured but is not missing in `options.headerCorrespondence` it is `null`.
* **maxSubjectLength:** `maxSubjectLength` is not available any more.
* **issuePrefixes:** `options.referenceKeywords` is now `options.referenceActions`
* **references:** `closes` now becomes `references` and it is loosely based the links above.
* **parser:** The regex for matching notes are loosen. The semicolon and space are optional. The `notes` object is no longer a key-value object but an array of note object, such as
```js
{
title: 'BREAKING AMEND',
text: 'some breaking change'
}
```
The detection of notes, closes, continueNote and isBody are mutually exclusive.
* **breaks:** Variable name related to `breaks` changes to `notes`, because "Important Notes" a more generic term. There is no functional changes.
* **stream:** It no longer skips the chunk if commit cannot be parsed. An empty string is passed to down stream
* **correspondence:** body and footer will be null if they are not found. type and subject are nullable too.

<a name="1.3.0"></a>
# [1.3.0](https://github.com/conventional-changelog/conventional-commits-parser/compare/v1.2.3...v1.3.0) (2016-10-15)


### Features

* support squash commits (#31) ([860c7a1](https://github.com/conventional-changelog/conventional-commits-parser/commit/860c7a1))



<a name="1.2.3"></a>
## [1.2.3](https://github.com/conventional-changelog/conventional-commits-parser/compare/v1.2.2...v1.2.3) (2016-08-06)


### Bug Fixes

* **regex:** do not treat it as note if there are texts after keywords ([9cb56bc](https://github.com/conventional-changelog/conventional-commits-parser/commit/9cb56bc))



<a name="1.2.2"></a>
## [1.2.2](https://github.com/conventional-changelog/conventional-commits-parser/compare/v1.2.1...v1.2.2) (2016-05-04)


### Bug Fixes

* **regex:** make getReferencePartsRegex stricter ([b8a9fda](https://github.com/conventional-changelog/conventional-commits-parser/commit/b8a9fda)), closes [#27](https://github.com/conventional-changelog/conventional-commits-parser/issues/27) [(#30](https://github.com/(/issues/30) [#27](https://github.com/conventional-changelog/conventional-commits-parser/issues/27) [#28](https://github.com/conventional-changelog/conventional-commits-parser/issues/28)



<a name="1.2.1"></a>
## [1.2.1](https://github.com/conventional-changelog/conventional-commits-parser/compare/v1.2.0...v1.2.1) (2016-04-24)


### Bug Fixes

* **mention:** fix mention matching ([43b32e7](https://github.com/conventional-changelog/conventional-commits-parser/commit/43b32e7)), closes [#26](https://github.com/conventional-changelog/conventional-commits-parser/issues/26)



<a name="1.2.0"></a>
# [1.2.0](https://github.com/conventional-changelog/conventional-commits-parser/compare/v1.1.0...v1.2.0) (2016-04-15)


### Features

* **mentions:** @someone in commit ([b2eabbf](https://github.com/conventional-changelog/conventional-commits-parser/commit/b2eabbf)), closes [#24](https://github.com/conventional-changelog/conventional-commits-parser/issues/24)



<a name="1.1.0"></a>
# [1.1.0](https://github.com/conventional-changelog/conventional-commits-parser/compare/v1.0.1...v1.1.0) (2016-04-10)


### Bug Fixes

* **notes:** note keywords must appear at the beginning of a sentence ([6e13789](https://github.com/conventional-changelog/conventional-commits-parser/commit/6e13789)), closes [#23](https://github.com/conventional-changelog/conventional-commits-parser/issues/23)

### Features

* **reference:** able to reference an issue without an action ([cf847b1](https://github.com/conventional-changelog/conventional-commits-parser/commit/cf847b1)), closes [#22](https://github.com/conventional-changelog/conventional-commits-parser/issues/22)



<a name="1.0.1"></a>
## [1.0.1](https://github.com/stevemao/conventional-commits-parser/compare/v1.0.0...v1.0.1) (2016-02-05)


### Bug Fixes

* **deps:** require split2 ([ad55810](https://github.com/stevemao/conventional-commits-parser/commit/ad55810))



<a name="1.0.0"></a>
# [1.0.0](https://github.com/stevemao/conventional-commits-parser/compare/v0.2.0...v1.0.0) (2016-02-05)




<a name="0.2.0"></a>
# [0.2.0](https://github.com/stevemao/conventional-commits-parser/compare/v0.1.2...v0.2.0) (2016-02-04)


### Features

* **note:** noteKeywords is case insensitive ([4442b86](https://github.com/stevemao/conventional-commits-parser/commit/4442b86)), closes [#21](https://github.com/stevemao/conventional-commits-parser/issues/21)
* **pullRequest:** Allow to skip and parse pull request header ([aa85033](https://github.com/stevemao/conventional-commits-parser/commit/aa85033)), closes [#20](https://github.com/stevemao/conventional-commits-parser/issues/20)
* **regex:** matching JIRA-123 like references ([5342f45](https://github.com/stevemao/conventional-commits-parser/commit/5342f45)), closes [#19](https://github.com/stevemao/conventional-commits-parser/issues/19)



<a name="0.1.2"></a>
## [0.1.2](https://github.com/stevemao/conventional-commits-parser/compare/v0.1.1...v0.1.2) (2015-09-18)


### Bug Fixes

* **parser:** do not trim spaces but newlines ([62e7bf5](https://github.com/stevemao/conventional-commits-parser/commit/62e7bf5))



<a name="0.1.1"></a>
## [0.1.1](https://github.com/stevemao/conventional-commits-parser/compare/v0.1.0...v0.1.1) (2015-09-12)


### Bug Fixes

* **newlines:** preserve newlines in a part ([beb3d05](https://github.com/stevemao/conventional-commits-parser/commit/beb3d05)), closes [#15](https://github.com/stevemao/conventional-commits-parser/issues/15)

### Features

* **reference:** expose prefix ([9962dda](https://github.com/stevemao/conventional-commits-parser/commit/9962dda)), closes [#17](https://github.com/stevemao/conventional-commits-parser/issues/17)
