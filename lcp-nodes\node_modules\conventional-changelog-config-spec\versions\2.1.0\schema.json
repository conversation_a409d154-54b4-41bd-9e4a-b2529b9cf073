{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Conventional Changelog Configuration", "description": "Describes the configuration options supported by conventional-config for upstream tooling.", "type": "object", "properties": {"header": {"type": "string", "description": "A string to be used as the main header section of the CHANGELOG.", "default": "# Changelog\n\n"}, "types": {"description": "An array of `type` objects representing the explicitly supported commit message types, and whether they should show up in generated `CHANGELOG`s.", "type": "array", "items": {"$ref": "#/definitions/type"}, "default": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "hidden": true}, {"type": "docs", "hidden": true}, {"type": "style", "hidden": true}, {"type": "refactor", "hidden": true}, {"type": "perf", "hidden": true}, {"type": "test", "hidden": true}]}, "preMajor": {"type": "boolean", "description": "Boolean indicating whether or not the action being run (generating CHANGELOG, recommendedBump, etc.) is being performed for a pre-major release (<1.0.0).\n This config setting will generally be set by tooling and not a user.", "default": false}, "commitUrlFormat": {"type": "string", "description": "A URL representing a specific commit at a hash.", "default": "{{host}}/{{owner}}/{{repository}}/commit/{{hash}}"}, "compareUrlFormat": {"type": "string", "description": "A URL representing the comparison between two git SHAs.", "default": "{{host}}/{{owner}}/{{repository}}/compare/{{previousTag}}...{{currentTag}}"}, "issueUrlFormat": {"type": "string", "description": "A URL representing the issue format (allowing a different URL format to be swapped in for Gitlab, Bitbucket, etc).", "default": "{{host}}/{{owner}}/{{repository}}/issues/{{id}}"}, "userUrlFormat": {"type": "string", "description": "A URL representing the a user's profile URL on GitHub, Gitlab, etc. This URL is used for substituting @bcoe with https://github.com/bcoe in commit messages.", "default": "{{host}}/{{user}}"}, "releaseCommitMessageFormat": {"type": "string", "description": "A string to be used to format the auto-generated release commit message.", "default": "chore(release): {{currentTag}}"}, "issuePrefixes": {"type": "array", "items": {"type": "string"}, "description": "An array of prefixes used to detect references to issues", "default": ["#"]}}, "definitions": {"type": {"description": "An object that describes a commit type's settings in the CHANGELOG", "type": "object", "properties": {"type": {"description": "A string used to match <type>s used in the Conventional Commits convention.", "type": "string"}, "section": {"description": "The section where the matched commit type will display in the CHANGELOG.", "type": "string"}, "hidden": {"description": "Set to `true` to hide matched commit types in the CHANGELOG.", "type": "boolean"}}, "required": ["type"], "oneOf": [{"required": ["section"]}, {"required": ["hidden"]}]}}}