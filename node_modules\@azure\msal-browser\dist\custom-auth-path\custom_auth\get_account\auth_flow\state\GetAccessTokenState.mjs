/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { AuthFlowStateBase } from '../../../core/auth_flow/AuthFlowState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * The completed state of the get access token flow.
 */
class GetAccessTokenCompletedState extends AuthFlowStateBase {
}
/**
 * The failed state of the get access token flow.
 */
class GetAccessTokenFailedState extends AuthFlowStateBase {
}

export { GetAccessTokenCompletedState, GetAccessTokenFailedState };
//# sourceMappingURL=GetAccessTokenState.mjs.map
