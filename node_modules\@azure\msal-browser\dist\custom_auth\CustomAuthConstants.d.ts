export declare const GrantType: {
    readonly PASSWORD: "password";
    readonly OOB: "oob";
    readonly CONTINUATION_TOKEN: "continuation_token";
    readonly REDIRECT: "redirect";
    readonly ATTRIBUTES: "attributes";
};
export declare const ChallengeType: {
    readonly PASSWORD: "password";
    readonly OOB: "oob";
    readonly REDIRECT: "redirect";
};
export declare const DefaultScopes: readonly [string, string, string];
export declare const HttpHeaderKeys: {
    readonly CONTENT_TYPE: "Content-Type";
    readonly X_MS_REQUEST_ID: "x-ms-request-id";
};
export declare const DefaultPackageInfo: {
    readonly SKU: "msal.browser";
    readonly VERSION: "4.15.0";
    readonly OS: "";
    readonly CPU: "";
};
export declare const ResetPasswordPollStatus: {
    readonly IN_PROGRESS: "in_progress";
    readonly SUCCEEDED: "succeeded";
    readonly FAILED: "failed";
    readonly NOT_STARTED: "not_started";
};
export declare const DefaultCustomAuthApiCodeLength = -1;
export declare const DefaultCustomAuthApiCodeResendIntervalInSec = 300;
export declare const PasswordResetPollingTimeoutInMs = 300000;
//# sourceMappingURL=CustomAuthConstants.d.ts.map