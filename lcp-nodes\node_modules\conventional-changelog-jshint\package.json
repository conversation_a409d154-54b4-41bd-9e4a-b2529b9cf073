{"name": "conventional-changelog-jshint", "version": "2.0.9", "description": "conventional-changelog jshint preset", "main": "index.js", "scripts": {"test-windows": "echo 'make work on windows'"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/conventional-changelog.git"}, "keywords": ["conventional-changelog", "j<PERSON>t", "preset"], "author": "<PERSON>", "engines": {"node": ">=10"}, "license": "ISC", "files": ["conventional-changelog.js", "conventional-recommended-bump.js", "index.js", "parser-opts.js", "writer-opts.js", "templates"], "bugs": {"url": "https://github.com/conventional-changelog/conventional-changelog/issues"}, "homepage": "https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-jshint#readme", "dependencies": {"compare-func": "^2.0.0", "q": "^1.5.1"}, "gitHead": "cc567b98facf71315f4b1620d81ce01d155efaca"}