{"name": "conventional-changelog-codemirror", "version": "2.0.8", "description": "conventional-changelog CodeMirror preset", "main": "index.js", "scripts": {"test-windows": "echo 'make work on windows'"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/conventional-changelog.git"}, "keywords": ["conventional-changelog", "codemirror", "preset"], "author": "<PERSON>", "engines": {"node": ">=10"}, "license": "ISC", "files": ["conventional-changelog.js", "conventional-recommended-bump.js", "index.js", "parser-opts.js", "writer-opts.js", "templates"], "bugs": {"url": "https://github.com/conventional-changelog/conventional-changelog/issues"}, "homepage": "https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-codemirror#readme", "dependencies": {"q": "^1.5.1"}, "gitHead": "cc567b98facf71315f4b1620d81ce01d155efaca"}