/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { SignInContinuationState } from '../../../sign_in/auth_flow/state/SignInContinuationState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * Represents the state that indicates the successful completion of a password reset operation.
 */
class ResetPasswordCompletedState extends SignInContinuationState {
}

export { ResetPasswordCompletedState };
//# sourceMappingURL=ResetPasswordCompletedState.mjs.map
