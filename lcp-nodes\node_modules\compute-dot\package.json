{"name": "compute-dot", "version": "1.1.0", "description": "Computes the dot product between two numeric arrays.", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "p<PERSON><PERSON><PERSON>@outlook.com"}], "scripts": {"test": "./node_modules/.bin/mocha", "test-cov": "./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --dir ./reports/coverage -- -R spec", "coveralls": "./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --dir ./reports/coveralls/coverage --report lcovonly -- -R spec && cat ./reports/coveralls/coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js && rm -rf ./reports/coveralls"}, "main": "./lib", "repository": {"type": "git", "url": "git://github.com/compute-io/dot.git"}, "keywords": ["compute.io", "compute", "computation", "algebra", "linear algebra", "dot product", "scalar product", "vector", "array", "calculus", "geometry", "mathematics", "math", "dot"], "bugs": {"url": "https://github.com/compute-io/dot/issues"}, "dependencies": {"validate.io-array": "^1.0.3", "validate.io-function": "^1.0.2"}, "devDependencies": {"chai": "2.x.x", "mocha": "2.x.x", "coveralls": "^2.11.1", "istanbul": "^0.3.0", "jshint": "2.x.x", "jshint-stylish": "^1.0.0"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}]}