# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.0.7](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-filter@2.0.7) (2020-11-05)

**Note:** Version bump only for package conventional-commits-filter





## [2.0.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-filter@2.0.6) (2020-05-08)

**Note:** Version bump only for package conventional-commits-filter





## [2.0.2](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-filter@2.0.2) (2019-04-10)


### Bug Fixes

* **filter:** replace `is-subset` with `lodash.ismatch` ([#377](https://github.com/conventional-changelog/conventional-changelog/issues/377)) ([fbcc92e](https://github.com/conventional-changelog/conventional-changelog/commit/fbcc92e))





## [2.0.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-filter@2.0.1) (2018-11-01)


### Bug Fixes

* Upgrade to Lerna 3, fix Node.js v11 error ([#385](https://github.com/conventional-changelog/conventional-changelog/issues/385)) ([cdef282](https://github.com/conventional-changelog/conventional-changelog/commit/cdef282))





<a name="2.0.0"></a>
# [2.0.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-filter@2.0.0) (2018-05-29)


### Chores

* **package:** set Node requirement to oldest supported LTS ([#329](https://github.com/conventional-changelog/conventional-changelog/issues/329)) ([cae2fe0](https://github.com/conventional-changelog/conventional-changelog/commit/cae2fe0))


### BREAKING CHANGES

* **package:** Set the package's minimum required Node version to be the oldest LTS
currently supported by the Node Release working group. At this time,
that is Node 6 (which is in its Maintenance LTS phase).




<a name="1.1.6"></a>
## [1.1.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-filter@1.1.6) (2018-03-22)




**Note:** Version bump only for package conventional-commits-filter

<a name="1.1.5"></a>
## [1.1.5](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-filter@1.1.5) (2018-02-24)




**Note:** Version bump only for package conventional-commits-filter

<a name="1.1.4"></a>
## [1.1.4](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-commits-filter@1.1.4) (2018-02-20)




**Note:** Version bump only for package conventional-commits-filter

<a name="1.1.3"></a>
## [1.1.3](https://github.com/stevemao/conventional-commits-filter/compare/<EMAIL>-commits-filter@1.1.3) (2018-02-13)




**Note:** Version bump only for package conventional-commits-filter

<a name="1.1.2"></a>
## [1.1.2](https://github.com/stevemao/conventional-commits-filter/compare/<EMAIL>-commits-filter@1.1.2) (2018-02-13)




**Note:** Version bump only for package conventional-commits-filter

<a name="1.1.1"></a>
## [1.1.1](https://github.com/stevemao/conventional-commits-filter/compare/<EMAIL>-commits-filter@1.1.1) (2017-12-08)


### Bug Fixes

* **filter:** only remove commits that reverted commits in the scope ([#226](https://github.com/stevemao/conventional-commits-filter/issues/226)) ([461dae6](https://github.com/stevemao/conventional-commits-filter/commit/461dae6))




<a name="1.1.0"></a>
# 1.1.0 (2017-11-13)


### Features

* migrate repo to lerna mono-repo ([793e823](https://github.com/stevemao/conventional-commits-filter/commit/793e823))
