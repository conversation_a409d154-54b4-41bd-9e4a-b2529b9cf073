/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { AuthFlowResultBase } from '../../../core/auth_flow/AuthFlowResultBase.mjs';
import { ResetPasswordResendCodeError } from '../error_type/ResetPasswordError.mjs';
import { ResetPasswordFailedState } from '../state/ResetPasswordFailedState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Result of resending code in a reset password operation.
 */
class ResetPasswordResendCodeResult extends AuthFlowResultBase {
    /**
     * Creates a new instance of ResetPasswordResendCodeResult.
     * @param state The state of the result.
     */
    constructor(state) {
        super(state);
    }
    /**
     * Creates a new instance of ResetPasswordResendCodeResult with an error.
     * @param error The error that occurred.
     * @returns {ResetPasswordResendCodeResult} A new instance of ResetPasswordResendCodeResult with the error set.
     */
    static createWithError(error) {
        const result = new ResetPasswordResendCodeResult(new ResetPasswordFailedState());
        result.error = new ResetPasswordResendCodeError(ResetPasswordResendCodeResult.createErrorData(error));
        return result;
    }
    /**
     * Checks if the result is in a failed state.
     */
    isFailed() {
        return this.state instanceof ResetPasswordFailedState;
    }
    /**
     * Checks if the result is in a code required state.
     */
    isCodeRequired() {
        /*
         * The instanceof operator couldn't be used here to check the state type since the circular dependency issue.
         * So we are using the constructor name to check the state type.
         */
        return (this.state.constructor?.name === "ResetPasswordCodeRequiredState");
    }
}

export { ResetPasswordResendCodeResult };
//# sourceMappingURL=ResetPasswordResendCodeResult.mjs.map
