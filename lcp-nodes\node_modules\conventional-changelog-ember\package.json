{"name": "conventional-changelog-ember", "version": "2.0.9", "description": "conventional-changelog ember preset", "main": "index.js", "scripts": {"test-windows": "mocha --timeout 30000"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/conventional-changelog.git"}, "keywords": ["conventional-changelog", "ember", "preset"], "author": "<PERSON>", "engines": {"node": ">=10"}, "license": "ISC", "files": ["conventional-changelog.js", "conventional-recommended-bump.js", "index.js", "parser-opts.js", "writer-opts.js", "templates"], "bugs": {"url": "https://github.com/conventional-changelog/conventional-changelog/issues"}, "homepage": "https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-ember#readme", "dependencies": {"q": "^1.5.1"}, "gitHead": "cc567b98facf71315f4b1620d81ce01d155efaca"}