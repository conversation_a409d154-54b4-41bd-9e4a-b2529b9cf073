{"name": "conventional-commits-filter", "version": "2.0.7", "description": "Filter out reverted commits parsed by conventional-commits-parser", "bugs": {"url": "https://github.com/conventional-changelog/conventional-changelog/issues"}, "homepage": "https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-commits-filter#readme", "repository": {"type": "git", "url": "https://github.com/conventional-changelog/conventional-changelog.git"}, "author": {"name": "<PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com", "url": "https://github.com/stevemao"}, "engines": {"node": ">=10"}, "files": ["index.js"], "keywords": ["filter", "conventional", "changelog", "commits"], "dependencies": {"lodash.ismatch": "^4.4.0", "modify-values": "^1.0.0"}, "scripts": {"test-windows": "mocha --timeout 30000"}, "license": "MIT", "gitHead": "cc567b98facf71315f4b1620d81ce01d155efaca"}