# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

### [3.1.25](https://github.com/conventional-changelog/conventional-changelog/compare/conventional-changelog-v3.1.24...conventional-changelog-v3.1.25) (2021-12-24)


### Bug Fixes

* **docs:** update list of available presets ([#871](https://github.com/conventional-changelog/conventional-changelog/issues/871)) ([2799851](https://github.com/conventional-changelog/conventional-changelog/commit/2799851f1915a42cb8498cf8959875badd07fd32))

## [3.1.24](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.24) (2020-11-05)

**Note:** Version bump only for package conventional-changelog





## [3.1.23](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.23) (2020-08-12)

**Note:** Version bump only for package conventional-changelog





## [3.1.22](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.22) (2020-06-20)

**Note:** Version bump only for package conventional-changelog





## [3.1.21](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.21) (2020-05-08)

**Note:** Version bump only for package conventional-changelog





## [3.1.20](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.20) (2020-05-08)

**Note:** Version bump only for package conventional-changelog





## [3.1.16](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.16) (2019-11-21)

**Note:** Version bump only for package conventional-changelog





## [3.1.15](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.15) (2019-11-14)

**Note:** Version bump only for package conventional-changelog





## [3.1.14](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.14) (2019-11-07)

**Note:** Version bump only for package conventional-changelog





## [3.1.13](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.13) (2019-10-24)

**Note:** Version bump only for package conventional-changelog





## [3.1.11](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.11) (2019-10-02)

**Note:** Version bump only for package conventional-changelog





## [3.1.10](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.10) (2019-07-29)

**Note:** Version bump only for package conventional-changelog





## [3.1.9](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.9) (2019-05-18)

**Note:** Version bump only for package conventional-changelog





## [3.1.8](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.8) (2019-05-05)

**Note:** Version bump only for package conventional-changelog





## [3.1.7](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.7) (2019-05-02)

**Note:** Version bump only for package conventional-changelog





## [3.1.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.6) (2019-05-02)

**Note:** Version bump only for package conventional-changelog





## [3.1.5](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.5) (2019-04-26)

**Note:** Version bump only for package conventional-changelog





## [3.1.4](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.4) (2019-04-24)

**Note:** Version bump only for package conventional-changelog





## [3.1.3](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.3) (2019-04-11)

**Note:** Version bump only for package conventional-changelog





## [3.1.2](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.2) (2019-04-11)

**Note:** Version bump only for package conventional-changelog





## [3.1.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.1) (2019-04-11)

**Note:** Version bump only for package conventional-changelog





# [3.1.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.1.0) (2019-04-10)


### Features

* conventionalcommits preset, preMajor config option ([#434](https://github.com/conventional-changelog/conventional-changelog/issues/434)) ([dde12fe](https://github.com/conventional-changelog/conventional-changelog/commit/dde12fe))
* creating highly configurable preset, based on conventionalcommits.org ([#421](https://github.com/conventional-changelog/conventional-changelog/issues/421)) ([f2fb240](https://github.com/conventional-changelog/conventional-changelog/commit/f2fb240))





## [3.0.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.0.6) (2019-02-14)

**Note:** Version bump only for package conventional-changelog





## [3.0.5](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.0.5) (2018-11-01)

**Note:** Version bump only for package conventional-changelog





## [3.0.4](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.0.4) (2018-11-01)

**Note:** Version bump only for package conventional-changelog





## [3.0.3](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.0.3) (2018-11-01)

**Note:** Version bump only for package conventional-changelog





## [3.0.2](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.0.2) (2018-11-01)

**Note:** Version bump only for package conventional-changelog





## [3.0.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.0.1) (2018-11-01)

**Note:** Version bump only for package conventional-changelog





# [3.0.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@3.0.0) (2018-11-01)


### Bug Fixes

* Upgrade to Lerna 3, fix Node.js v11 error ([#385](https://github.com/conventional-changelog/conventional-changelog/issues/385)) ([cdef282](https://github.com/conventional-changelog/conventional-changelog/commit/cdef282))


### chore

* force breaking change ([f6d506d](https://github.com/conventional-changelog/conventional-changelog/commit/f6d506d))


### BREAKING CHANGES

* forcing a breaking semver change based on https://github.com/conventional-changelog/conventional-changelog/pull/385





       <a name="2.0.3"></a>
## [2.0.3](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@2.0.3) (2018-08-21)




**Note:** Version bump only for package conventional-changelog

       <a name="2.0.2"></a>
## [2.0.2](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@2.0.2) (2018-08-21)




**Note:** Version bump only for package conventional-changelog

<a name="2.0.1"></a>
## [2.0.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@2.0.1) (2018-06-06)




**Note:** Version bump only for package conventional-changelog

<a name="2.0.0"></a>
# [2.0.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@2.0.0) (2018-05-29)


### Chores

* **package:** set Node requirement to oldest supported LTS ([#329](https://github.com/conventional-changelog/conventional-changelog/issues/329)) ([cae2fe0](https://github.com/conventional-changelog/conventional-changelog/commit/cae2fe0))


### BREAKING CHANGES

* **package:** Set the package's minimum required Node version to be the oldest LTS
currently supported by the Node Release working group. At this time,
that is Node 6 (which is in its Maintenance LTS phase).




<a name="1.1.24"></a>
## [1.1.24](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.24) (2018-04-16)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.23"></a>
## [1.1.23](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.23) (2018-03-28)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.22"></a>
## [1.1.22](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.22) (2018-03-27)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.21"></a>
## [1.1.21](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.21) (2018-03-27)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.20"></a>
## [1.1.20](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.20) (2018-03-27)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.19"></a>
## [1.1.19](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.19) (2018-03-22)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.18"></a>
## [1.1.18](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.18) (2018-03-03)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.17"></a>
## [1.1.17](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.17) (2018-02-24)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.16"></a>
## [1.1.16](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.16) (2018-02-20)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.15"></a>
## [1.1.15](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.15) (2018-02-13)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.14"></a>
## [1.1.14](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.14) (2018-02-13)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.13"></a>
## [1.1.13](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.13) (2018-02-13)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.12"></a>
## [1.1.12](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.12) (2018-02-12)


### Bug Fixes

* **conventional-changelog:** support scoped presets ([0f08267](https://github.com/conventional-changelog/conventional-changelog/commit/0f08267))




<a name="1.1.11"></a>
## [1.1.11](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.11) (2018-02-05)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.10"></a>
## [1.1.10](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.10) (2018-01-29)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.9"></a>
## [1.1.9](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.9) (2017-12-18)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.8"></a>
## [1.1.8](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.8) (2017-12-08)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.7"></a>
## [1.1.7](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.7) (2017-11-13)




**Note:** Version bump only for package conventional-changelog

<a name="1.1.6"></a>
## [1.1.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.6) (2017-10-01)

<a name="1.1.5"></a>
## [1.1.5](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.5) (2017-09-01)

<a name="1.1.4"></a>
## [1.1.4](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.4) (2017-07-17)

<a name="1.1.3"></a>
## [1.1.3](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog@1.1.3) (2017-03-11)

<a name="1.1.0"></a>
# [1.1.0](https://github.com/ajoslin/conventional-changelog/compare/v1.0.2...v1.1.0) (2016-02-13)




<a name="1.0.2"></a>
## [1.0.2](https://github.com/ajoslin/conventional-changelog/compare/v1.0.1...v1.0.2) (2016-02-13)




<a name="1.0.1"></a>
## [1.0.1](https://github.com/ajoslin/conventional-changelog/compare/v1.0.0...v1.0.1) (2016-02-05)




<a name="1.0.0"></a>
# [1.0.0](https://github.com/ajoslin/conventional-changelog/compare/v0.5.3...v1.0.0) (2016-02-05)


### Bug Fixes

* **bin:** no cli anymore ([65a9f4b](https://github.com/ajoslin/conventional-changelog/commit/65a9f4b))
* **cli:** print the stack if verbose ([81860ab](https://github.com/ajoslin/conventional-changelog/commit/81860ab))

### Features

* **module:** broken down into smaller modules ([48580b0](https://github.com/ajoslin/conventional-changelog/commit/48580b0))


### BREAKING CHANGES

* module: Presets have their own repos. cli and core have their own repos. This one is a pure wrapper. In the core, a new api `options.config` is added.



<a name="0.5.3"></a>
## [0.5.3](https://github.com/ajoslin/conventional-changelog/compare/v0.5.2...v0.5.3) (2015-12-25)


### Bug Fixes

* **defaults:** do not throw if no package.json found ([906a904](https://github.com/ajoslin/conventional-changelog/commit/906a904)), closes [#123](https://github.com/ajoslin/conventional-changelog/issues/123)



<a name="0.5.2"></a>
## [0.5.2](https://github.com/ajoslin/conventional-changelog/compare/v0.5.1...v0.5.2) (2015-12-23)


### Features

* **preset:** relax JSHint message requirements ([5aa5e32](https://github.com/ajoslin/conventional-changelog/commit/5aa5e32)), closes [#129](https://github.com/ajoslin/conventional-changelog/issues/129)



<a name="0.5.1"></a>
## [0.5.1](https://github.com/ajoslin/conventional-changelog/compare/v0.5.0...v0.5.1) (2015-10-20)


### Bug Fixes

* **options:** fix losing parserOpts without preset ([e6a9cf3](https://github.com/ajoslin/conventional-changelog/commit/e6a9cf3)), closes [#116](https://github.com/ajoslin/conventional-changelog/issues/116)



<a name="0.5.0"></a>
# [0.5.0](https://github.com/ajoslin/conventional-changelog/compare/v0.4.3...v0.5.0) (2015-09-30)


### Bug Fixes

* **unknown-host:** fallback to use the whole repo url ([514c4e2](https://github.com/ajoslin/conventional-changelog/commit/514c4e2)), closes [#98](https://github.com/ajoslin/conventional-changelog/issues/98)

### Features

* **dep:** bump conventional-changelog-writer to ^0.4.1 ([cbfb222](https://github.com/ajoslin/conventional-changelog/commit/cbfb222))
* **pkg:** load closest package.json by default ([5942809](https://github.com/ajoslin/conventional-changelog/commit/5942809)), closes [#91](https://github.com/ajoslin/conventional-changelog/issues/91)
* **template:** add `scope` to breaking changes ([d2fdd44](https://github.com/ajoslin/conventional-changelog/commit/d2fdd44)), closes [#93](https://github.com/ajoslin/conventional-changelog/issues/93)


### BREAKING CHANGES

* dep: Use the new conventional-changelog-writer syntax. `notes` in `noteGroups` is not an array of simple string any more but object. You must use `note.text` to access the equivalent of previous `note`.



<a name="0.4.3"></a>
## [0.4.3](https://github.com/ajoslin/conventional-changelog/compare/v0.4.2...v0.4.3) (2015-09-04)


### Bug Fixes

* **cli:** require at the correct directory ([feceb8b](https://github.com/ajoslin/conventional-changelog/commit/feceb8b))
* **cli:** require file with absolute path ([7f68b3e](https://github.com/ajoslin/conventional-changelog/commit/7f68b3e)), closes [#96](https://github.com/ajoslin/conventional-changelog/issues/96)
* **error:** emit error if there is any async ([831c960](https://github.com/ajoslin/conventional-changelog/commit/831c960))
* **preset:** if scope is * for angular it should be ignored ([91094c9](https://github.com/ajoslin/conventional-changelog/commit/91094c9))
* **templates:** add a missing newline after notes ([e1ae4b2](https://github.com/ajoslin/conventional-changelog/commit/e1ae4b2))



<a name="0.4.2"></a>
## [0.4.2](https://github.com/ajoslin/conventional-changelog/compare/v0.4.1...v0.4.2) (2015-08-17)


### Bug Fixes

* **preset:** jshint may contain BREAKING CHANGE footer ([2683cee](https://github.com/ajoslin/conventional-changelog/commit/2683cee))



<a name="0.4.1"></a>
## [0.4.1](https://github.com/ajoslin/conventional-changelog/compare/v0.4.0...v0.4.1) (2015-08-15)


### Features

* **hosts:** add support for gitlab.com repositories ([bc1746b](https://github.com/ajoslin/conventional-changelog/commit/bc1746b)), closes [#86](https://github.com/ajoslin/conventional-changelog/issues/86) [#88](https://github.com/ajoslin/conventional-changelog/issues/88)



<a name="0.4.0"></a>
# [0.4.0](https://github.com/ajoslin/conventional-changelog/compare/v0.3.2...v0.4.0) (2015-08-15)


### Features

* **preset:** add codemirror ([bc480f8](https://github.com/ajoslin/conventional-changelog/commit/bc480f8))



<a name="0.3.2"></a>
## [0.3.2](https://github.com/ajoslin/conventional-changelog/compare/v0.3.1...v0.3.2) (2015-08-13)


### Bug Fixes

* **pkg:** should always read package.json ([fcaac48](https://github.com/ajoslin/conventional-changelog/commit/fcaac48))
* **preset:** should still work if preset is wrong ([bc8240e](https://github.com/ajoslin/conventional-changelog/commit/bc8240e))

### Features

* **context:** attach your packageData ([7138206](https://github.com/ajoslin/conventional-changelog/commit/7138206))



<a name="0.3.1"></a>
## [0.3.1](https://github.com/ajoslin/conventional-changelog/compare/v0.3.0...v0.3.1) (2015-08-11)


### Bug Fixes

* **cli:** gracefully handle it if infile is ENOENT ([12f2889](https://github.com/ajoslin/conventional-changelog/commit/12f2889))



<a name="0.3.0"></a>
# [0.3.0](https://github.com/ajoslin/conventional-changelog/compare/v0.2.1...v0.3.0) (2015-08-09)


### Bug Fixes

* **ember:** when cannot find header in pr ([9d833fd](https://github.com/ajoslin/conventional-changelog/commit/9d833fd))
* **err:** better error handling ([7f0e3f5](https://github.com/ajoslin/conventional-changelog/commit/7f0e3f5))
* **preset:** gitRawCommitsOpts should be counted ([2bb4b47](https://github.com/ajoslin/conventional-changelog/commit/2bb4b47))
* **previousTag:** incase there is no commits in the first release ([8d9f363](https://github.com/ajoslin/conventional-changelog/commit/8d9f363))

### Features

* **compareLink:** link version numbers to compare ([f0dbadb](https://github.com/ajoslin/conventional-changelog/commit/f0dbadb))
* **context:** attach gitSemverTags ([ac098a3](https://github.com/ajoslin/conventional-changelog/commit/ac098a3)), closes [#79](https://github.com/ajoslin/conventional-changelog/issues/79)
* **finalizeContext:** `context.previousVersion` defaults to a previous version of generated log ([a2df9ca](https://github.com/ajoslin/conventional-changelog/commit/a2df9ca))
* **preset:** add ember ([0ccb8da](https://github.com/ajoslin/conventional-changelog/commit/0ccb8da))
* **preset:** add express ([c4b20b1](https://github.com/ajoslin/conventional-changelog/commit/c4b20b1))
* **preset:** add jscs ([1313d55](https://github.com/ajoslin/conventional-changelog/commit/1313d55))


### BREAKING CHANGES

* `options.transform` is a function instead of a stream. This is more elegant and easier to handle any error.


<a name="0.2.1"></a>
## 0.2.1 (2015-07-24)


### Features

* **transform:** put the default from presets to core ([946c1da](https://github.com/ajoslin/conventional-changelog/commit/946c1da))



<a name="0.2.0"></a>
# 0.2.0 (2015-07-24)


### Features

* **releaseCount:** replace allBlocks ([fd1ce9d](https://github.com/ajoslin/conventional-changelog/commit/fd1ce9d))

### Performance Improvements

* **context:** use the parsed host type if possible ([305b3d5](https://github.com/ajoslin/conventional-changelog/commit/305b3d5))


### BREAKING CHANGES

* `allBlocks` is removed. Use `releaseCount` instead.



<a name="0.1.3"></a>
## 0.1.3 (2015-07-22)


### Bug Fixes

* **preset:** work if more than two semver tags ([b8ad049](https://github.com/ajoslin/conventional-changelog/commit/b8ad049))



<a name="0.1.2"></a>
## 0.1.2 (2015-07-22)


### Bug Fixes

* **pkg.transform:** transform should be performed before normalizing pkgData ([7c59bfd](https://github.com/ajoslin/conventional-changelog/commit/7c59bfd))
* **pkgRepo:** get version even if no `repo.type` ([1016e08](https://github.com/ajoslin/conventional-changelog/commit/1016e08)), closes [#74](https://github.com/ajoslin/conventional-changelog/issues/74) [#75](https://github.com/ajoslin/conventional-changelog/issues/75)



<a name="0.1.0"></a>
# 0.1.0 (2015-07-20)

Stable version



<a name="0.1.0-beta.3"></a>
# 0.1.0-beta.3 (2015-07-17)


### Bug Fixes

* **cli:** map pkg.path correctly ([c9a59a7](https://github.com/ajoslin/conventional-changelog/commit/c9a59a7))



<a name="0.1.0-beta.2"></a>
# 0.1.0-beta.2 (2015-07-17)


### Bug Fixes

* **parserOpts:** options.warn should overwrite preset.parserOpts.warn ([94f40cf](https://github.com/ajoslin/conventional-changelog/commit/94f40cf))
* **parserOpts:** preset.parserOpts should overwrite hostOpts ([8bb7451](https://github.com/ajoslin/conventional-changelog/commit/8bb7451))
* **preset:** no length limit for eslint ([70b1a76](https://github.com/ajoslin/conventional-changelog/commit/70b1a76))

### Features

* **pkg:** add a transform function ([e576563](https://github.com/ajoslin/conventional-changelog/commit/e576563))
* **preset:** add atom ([714b694](https://github.com/ajoslin/conventional-changelog/commit/714b694))
* **preset:** add eslint ([af37323](https://github.com/ajoslin/conventional-changelog/commit/af37323))


### BREAKING CHANGES

* `options.pkg` is now an object. `options.pkg.path` is the path of the package.json and `options.pkg.transform` is the function to transform the package.json.



<a name="0.1.0-beta.1"></a>
# 0.1.0-beta.1 (2015-07-15)


### Bug Fixes

* **stream:** is object mode if writerOpts.includeDetails is true ([38e3faa](https://github.com/ajoslin/conventional-changelog/commit/38e3faa))

### Features

* **owner:** add context.owner support ([87d60b4](https://github.com/ajoslin/conventional-changelog/commit/87d60b4))



<a name="0.1.0-alpha.3"></a>
# 0.1.0-alpha.3 (2015-07-06)


### Bug Fixes

* conventional-commits-writer -> conventional-changelog-writer ([aa6cbd6](https://github.com/ajoslin/conventional-changelog/commit/aa6cbd6))
* **hosts:** spelling mistake of host bitbucket ([1e30d54](https://github.com/ajoslin/conventional-changelog/commit/1e30d54)), closes [#68](https://github.com/ajoslin/conventional-changelog/issues/68) [#69](https://github.com/ajoslin/conventional-changelog/issues/69)

### Features

* **angular:** add new revert opts for the parser ([9e15f01](https://github.com/ajoslin/conventional-changelog/commit/9e15f01))
* **preset:** add jshint ([384e6ce](https://github.com/ajoslin/conventional-changelog/commit/384e6ce))



<a name="0.1.0-alpha.2"></a>
# 0.1.0-alpha.2 (2015-06-29)

Bump deps and fix https://github.com/ajoslin/conventional-changelog/issues/64



<a name="0.1.0-alpha.1"></a>
# 0.1.0-alpha.1 (2015-06-24)


### Bug Fixes

* **err:** emit error if there is any in gitRawCommits and conventionalCommitsParser ([00ac3c1](https://github.com/ajoslin/conventional-changelog/commit/00ac3c1))

### Features

* **cli:** first commit of cli ([d74b96b](https://github.com/ajoslin/conventional-changelog/commit/d74b96b)), closes [#31](https://github.com/ajoslin/conventional-changelog/issues/31)
* **issuePrefixes:** default for the hosts ([b1c3ee9](https://github.com/ajoslin/conventional-changelog/commit/b1c3ee9)), closes [#59](https://github.com/ajoslin/conventional-changelog/issues/59) [#60](https://github.com/ajoslin/conventional-changelog/issues/60)
* **rewrite:** rewrite this module ([7c48e0d](https://github.com/ajoslin/conventional-changelog/commit/7c48e0d)), closes [#50](https://github.com/ajoslin/conventional-changelog/issues/50) [#45](https://github.com/ajoslin/conventional-changelog/issues/45) [#40](https://github.com/ajoslin/conventional-changelog/issues/40) [#22](https://github.com/ajoslin/conventional-changelog/issues/22) [#13](https://github.com/ajoslin/conventional-changelog/issues/13) [#12](https://github.com/ajoslin/conventional-changelog/issues/12) [#54](https://github.com/ajoslin/conventional-changelog/issues/54) [#51](https://github.com/ajoslin/conventional-changelog/issues/51)


### BREAKING CHANGES

* This module is rewritten so API is changed and it is not backward compatible. Please check docs and all the submodules including git-raw-commits, conventional-commits-parser and conventional-changelog-writer for more information.



<a name"0.0.17"></a>
### 0.0.17 (2015-04-03)


#### Bump deps


<a name"0.0.16"></a>
### 0.0.16 (2015-03-19)


#### Bug Fixes

* **git:** generate the correct cmd of git log when there is no tags ([dcd7551f](https://github.com/ajoslin/conventional-changelog/commit/dcd7551f), closes [#47](https://github.com/ajoslin/conventional-changelog/issues/47), [#48](https://github.com/ajoslin/conventional-changelog/issues/48))


<a name"0.0.15"></a>
### 0.0.15 (2015-03-19)


#### Bug Fixes

* **log:** correct out put for `options.from` and `options.to` ([31ddb112](https://github.com/ajoslin/conventional-changelog/commit/31ddb112), closes [#47](https://github.com/ajoslin/conventional-changelog/issues/47))


<a name"0.0.14"></a>
### 0.0.14 (2015-03-14)


#### Bug Fixes

* **writeLog:** fix require statement for Writer ([a478f806](https://github.com/ajoslin/conventional-changelog/commit/a478f806))


<a name"0.0.13"></a>
### 0.0.13 (2015-03-13)


#### Bug Fixes

* **first commit:** add first commit to changelog ([386cd404](https://github.com/ajoslin/conventional-changelog/commit/386cd404))
* **git:** use --abbrev=0 to only get tag from same branch ([69cfb5c6](https://github.com/ajoslin/conventional-changelog/commit/69cfb5c6))
* **header:** fix no `<a>` if options.repository is provided ([7cb5cb56](https://github.com/ajoslin/conventional-changelog/commit/7cb5cb56), closes [#26](https://github.com/ajoslin/conventional-changelog/issues/26))
* **pkg:** handle the situation where package.json cannot be found ([518bc56e](https://github.com/ajoslin/conventional-changelog/commit/518bc56e))
* **version:** default version is read from package.json properly ([f684b9be](https://github.com/ajoslin/conventional-changelog/commit/f684b9be))


#### Features

* **defaults:** version and repository are read from package.json ([cb1feb7d](https://github.com/ajoslin/conventional-changelog/commit/cb1feb7d), closes [#38](https://github.com/ajoslin/conventional-changelog/issues/38))


### 0.0.11 "reorder" (2014-05-28)


#### Features

* **changelog:** add versionText, patchVersionText options ([9d8e0548](https://github.com/ajoslin/conventional-changelog/commit/9d8e05480771f881c33e535f922401637f11861c))


#### Breaking Changes

* 
Removed versionLink and patchVersionLink options, and went back to the
default title output from 0.0.9.

If you wish to have a link to your version, simply customize the versionText
and patchVersionText options.

([9d8e0548](https://github.com/ajoslin/conventional-changelog/commit/9d8e05480771f881c33e535f922401637f11861c))


### 0.0.10 "revise" (2014-05-28)


#### Bug Fixes

* **changelog:** put commit range into quotes so it can fetch commits with special characters ([76e2f185](https://github.com/ajoslin/conventional-changelog/commit/76e2f185b6542e7fe731c4666323fac68b9e2202), closes [#10](https://github.com/ajoslin/conventional-changelog/issues/10))


#### Features

* **changelog:** add support for scope with spaces ([b5e43b75](https://github.com/ajoslin/conventional-changelog/commit/b5e43b75c6caabc357e4bce0eb64316fbe153ecf), closes [#9](https://github.com/ajoslin/conventional-changelog/issues/9))
* **git:** allow period-separated closing and lowercase closing ([6835af55](https://github.com/ajoslin/conventional-changelog/commit/6835af55d57b62ff6dcebf624f3c6108cbc36b8e))
* **writer:** add tag hyperlink support ([9640cc27](https://github.com/ajoslin/conventional-changelog/commit/9640cc279ca9c513b1378eb55b5a7d576fd78bf5))


<a name="0.0.9"></a>
### 0.0.9 "change" (2014-05-06)


#### Bug Fixes

* **changelog:** make sure breaking changes are separated by two newlines ([85152160](https://github.com/ajoslin/conventional-changelog/commit/8515216093eaa7f997dc506675d729a0e41578d6))


#### Features

* **changelog:** also add `Resolves #xx` to closes section ([06ff3ea9](https://github.com/ajoslin/conventional-changelog/commit/06ff3ea9b0c8baf2fae6167a99b6826a44a0c768))


<a name="0.0.8"></a>
### 0.0.8 "refine" (2014-04-10)


#### Features

* **changelog:** change options.codename to options.subtitle ([a00fea52](https://github.com/ajoslin/conventional-changelog/commit/a00fea521667533809419af6a66b20ae4ce96e3b))


<a name="0.0.7"></a>
### 0.0.7 "delta" (2014-04-10)


#### Features

* **changelog:** add options.codename ([01f40cb6](https://github.com/ajoslin/conventional-changelog/commit/01f40cb6efe2180ede9c1e520da76877eb895759))


<a name="0.0.6"></a>
### 0.0.6 (2014-01-23)

#### Bug Fixes

* **git:** sort tags correctly ([7318bb05](https://github.com/ajoslin/conventional-changelog/commit/7318bb05d335bfa6886e816bec4fc57cd395c2c6))

<a name="0.0.5"></a>
### 0.0.5 (2014-01-23)

#### Miscellaneous

* More specific errors given through done callback
* Improved logging

<a name="0.0.4"></a>
### 0.0.4 (2014-01-04)

#### Bug Fixes

* **version:** do not try to figure out version ([5c99b7279b97352a93eca0ee37f198783d64f423](https://github.com/ajoslin/conventional-changelog/commit/5c99b7279b97352a93eca0ee37f198783d64f423))

<a name="0.0.2"></a>
### 0.0.2 (2014-01-04)

#### Features

* create conventional-changelog module ([dd1959d7b2c18846b12b088b47345a2a171c1309](https://github.com/ajoslin/conventional-changelog/commit/dd1959d7b2c18846b12b088b47345a2a171c1309))
