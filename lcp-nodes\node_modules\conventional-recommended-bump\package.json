{"name": "conventional-recommended-bump", "version": "6.1.0", "description": "Get a recommended version bump based on conventional commits", "bugs": {"url": "https://github.com/conventional-changelog/conventional-changelog/issues"}, "homepage": "https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-recommended-bump#readme", "author": {"name": "<PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com", "url": "https://github.com/stevemao"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/conventional-changelog.git"}, "license": "MIT", "engines": {"node": ">=10"}, "files": ["index.js", "cli.js", "preset-resolver.js"], "keywords": ["conventional-recommended-bump", "recommend", "conventional", "bump"], "dependencies": {"concat-stream": "^2.0.0", "conventional-changelog-preset-loader": "^2.3.4", "conventional-commits-filter": "^2.0.7", "conventional-commits-parser": "^3.2.0", "git-raw-commits": "^2.0.8", "git-semver-tags": "^4.1.1", "meow": "^8.0.0", "q": "^1.5.1"}, "scripts": {"test-windows": "mocha --timeout 30000 ./test/preset-resolver.spec.js"}, "bin": "cli.js", "devDependencies": {"conventional-changelog-conventionalcommits": "^4.5.0"}}