# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

### [5.0.1](https://github.com/conventional-changelog/conventional-changelog/compare/conventional-changelog-writer-v5.0.0...conventional-changelog-writer-v5.0.1) (2021-12-29)


### Bug Fixes

* **deps:** patch the handlebars package for CVE-2021-23369 ([1cfc3a1](https://github.com/conventional-changelog/conventional-changelog/commit/1cfc3a1600a11a61d1a9e8d3051d1101cfaa36f1))

## [5.0.0](https://www.github.com/conventional-changelog/conventional-changelog/compare/v4.1.0...v5.0.0) (2020-12-30)


### ⚠ BREAKING CHANGES

* nested object properties no longer supported when sorting

### Bug Fixes

* drop compare-func making sort consistent across node versions ([#729](https://www.github.com/conventional-changelog/conventional-changelog/issues/729)) ([e0081a8](https://www.github.com/conventional-changelog/conventional-changelog/commit/e0081a829133891e2def4a7b7ee5fa25f1440049))

## [4.1.0](https://www.github.com/conventional-changelog/conventional-changelog/compare/conventional-changelog-writer@4.0.18...v4.1.0) (2020-12-29)


### Features

* add helper for parsing array of commits ([#711](https://www.github.com/conventional-changelog/conventional-changelog/issues/711)) ([e869fe6](https://www.github.com/conventional-changelog/conventional-changelog/commit/e869fe67548b210508a9df0ce99180164b740e65))

## [4.0.18](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.18) (2020-11-05)


### Bug Fixes

* **deps:** update dependency through2 to v4 ([#657](https://github.com/conventional-changelog/conventional-changelog/issues/657)) ([7ae618c](https://github.com/conventional-changelog/conventional-changelog/commit/7ae618c81491841e5b1d796d3933aac0c54bc312))





## [4.0.17](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.17) (2020-06-20)


### Bug Fixes

* **deps:** update dependency compare-func to v2 ([#647](https://github.com/conventional-changelog/conventional-changelog/issues/647)) ([de4f630](https://github.com/conventional-changelog/conventional-changelog/commit/de4f6309403ca0d46b7c6235052f4dca61ea15bc))





## [4.0.16](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.16) (2020-05-08)


### Bug Fixes

* **deps:** address CVE in meow ([#642](https://github.com/conventional-changelog/conventional-changelog/issues/642)) ([46311d2](https://github.com/conventional-changelog/conventional-changelog/commit/46311d2932b367f370d06c4e447b8dcf4bc4e83f))





## [4.0.15](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.15) (2020-05-08)


### Bug Fixes

* **deps:** update yargs-parser to move off a flagged-vulnerable version. ([#635](https://github.com/conventional-changelog/conventional-changelog/issues/635)) ([aafc0f0](https://github.com/conventional-changelog/conventional-changelog/commit/aafc0f00412c3e4b23b8418300e5a570a48fe24d))





## [4.0.11](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.11) (2019-11-14)


### Bug Fixes

* add types for cli flags ([#551](https://github.com/conventional-changelog/conventional-changelog/issues/551)) ([bf1d64a](https://github.com/conventional-changelog/conventional-changelog/commit/bf1d64aeaf8f262d4b2beec02d2aebb78df7343b))





## [4.0.10](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.10) (2019-10-24)


### Bug Fixes

* **deps:** update lodash to fix security issues ([#535](https://github.com/conventional-changelog/conventional-changelog/issues/535)) ([ac43f51](https://github.com/conventional-changelog/conventional-changelog/commit/ac43f51de1f3b597c32f7f8442917a2d06199018))





## [4.0.8](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.8) (2019-10-02)

**Note:** Version bump only for package conventional-changelog-writer





## [4.0.7](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.7) (2019-07-29)

**Note:** Version bump only for package conventional-changelog-writer





## [4.0.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.6) (2019-05-18)


### Bug Fixes

* **deps:** update dependency semver to v6 ([#458](https://github.com/conventional-changelog/conventional-changelog/issues/458)) ([efaa7bb](https://github.com/conventional-changelog/conventional-changelog/commit/efaa7bb))





## [4.0.5](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.5) (2019-04-11)

**Note:** Version bump only for package conventional-changelog-writer





## [4.0.4](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.4) (2019-04-10)


### Bug Fixes

* **deps:** update dependency through2 to v3 ([#392](https://github.com/conventional-changelog/conventional-changelog/issues/392)) ([26fe91f](https://github.com/conventional-changelog/conventional-changelog/commit/26fe91f))





## [4.0.3](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.3) (2019-02-14)

**Note:** Version bump only for package conventional-changelog-writer





## [4.0.2](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.2) (2018-11-01)


### Bug Fixes

* bad release of conventional-changelog-writer ([b5da9af](https://github.com/conventional-changelog/conventional-changelog/commit/b5da9af))





## [4.0.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.1) (2018-11-01)


### Bug Fixes

* Upgrade to Lerna 3, fix Node.js v11 error ([#385](https://github.com/conventional-changelog/conventional-changelog/issues/385)) ([cdef282](https://github.com/conventional-changelog/conventional-changelog/commit/cdef282))





      <a name="4.0.0"></a>
# [4.0.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@4.0.0) (2018-05-29)


### Chores

* **package:** set Node requirement to oldest supported LTS ([#329](https://github.com/conventional-changelog/conventional-changelog/issues/329)) ([cae2fe0](https://github.com/conventional-changelog/conventional-changelog/commit/cae2fe0))


### Code Refactoring

* remove anchor from header templates ([#301](https://github.com/conventional-changelog/conventional-changelog/issues/301)) ([346f24f](https://github.com/conventional-changelog/conventional-changelog/commit/346f24f)), closes [#186](https://github.com/conventional-changelog/conventional-changelog/issues/186)


### BREAKING CHANGES

* **package:** Set the package's minimum required Node version to be the oldest LTS
currently supported by the Node Release working group. At this time,
that is Node 6 (which is in its Maintenance LTS phase).
* Anchor tags are removed from the changelog header templates. The
rendered Markdown will no longer contain anchor tags proceeding the
version number header that constitutes the changelog header. This means
that consumers of rendered markdown will not be able to use a URL that
has been constructed to contain a version number anchor tag reference,
since the anchor tag won't exist in the rendered markdown.

It's stronly recomended consumers use the full URL path to the release
page for a given version, as that URL is a permalink to that verison,
contains all relavent release information, and does not, otherwise, rely
on the anchor tag being excessible from the current page view.

As an example, for version `2.0.0` of a GitHub project, the following
URL should be used:
- https://github.com/conventional-changelog/releaser-tools/releases/tag/v2.0.0




      <a name="3.0.9"></a>
## [3.0.9](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@3.0.9) (2018-03-28)


### Bug Fixes

* revert previous change ([2f4530f](https://github.com/conventional-changelog/conventional-changelog/commit/2f4530f))




<a name="3.0.8"></a>
## [3.0.8](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@3.0.8) (2018-03-27)




**Note:** Version bump only for package conventional-changelog-writer

<a name="3.0.7"></a>
## [3.0.7](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@3.0.7) (2018-03-27)




**Note:** Version bump only for package conventional-changelog-writer

<a name="3.0.6"></a>
## [3.0.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@3.0.6) (2018-03-27)




**Note:** Version bump only for package conventional-changelog-writer

<a name="3.0.5"></a>
## [3.0.5](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@3.0.5) (2018-03-22)




**Note:** Version bump only for package conventional-changelog-writer

<a name="3.0.4"></a>
## [3.0.4](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@3.0.4) (2018-02-24)




**Note:** Version bump only for package conventional-changelog-writer

<a name="3.0.3"></a>
## [3.0.3](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-writer@3.0.3) (2018-02-20)




**Note:** Version bump only for package conventional-changelog-writer

<a name="3.0.2"></a>
## [3.0.2](https://github.com/conventional-changelog/conventional-changelog-writer/compare/<EMAIL>-changelog-writer@3.0.2) (2018-02-13)




**Note:** Version bump only for package conventional-changelog-writer

<a name="3.0.1"></a>
## [3.0.1](https://github.com/conventional-changelog/conventional-changelog-writer/compare/<EMAIL>-changelog-writer@3.0.1) (2018-02-13)




**Note:** Version bump only for package conventional-changelog-writer

<a name="3.0.0"></a>
# [3.0.0](https://github.com/conventional-changelog/conventional-changelog-writer/compare/<EMAIL>-changelog-writer@3.0.0) (2018-01-29)


### Bug Fixes

* **writer:** normalize release headings ([#237](https://github.com/conventional-changelog/conventional-changelog-writer/issues/237)) ([9e87dc3](https://github.com/conventional-changelog/conventional-changelog-writer/commit/9e87dc3)), closes [/github.com/conventional-changelog/conventional-changelog/issues/214#issuecomment-326681934](https://github.com//github.com/conventional-changelog/conventional-changelog/issues/214/issues/issuecomment-326681934)


### BREAKING CHANGES

* **writer:** Logic for generating release headings has been changed to make all
heading levels the same (`##`/`h2`) for better compatibility with
screen readers and parsers, and to conform to HTML semantics. Patch
release titles are now wrapped in a `<small>` tag to maintain the
visual hierarchy of the previous style.

Fixes https://github.com/conventional-changelog/conventional-changelog/issues/214




<a name="2.0.3"></a>
## [2.0.3](https://github.com/conventional-changelog/conventional-changelog-writer/compare/<EMAIL>-changelog-writer@2.0.3) (2017-12-08)




**Note:** Version bump only for package conventional-changelog-writer

<a name="2.0.2"></a>
## [2.0.2](https://github.com/conventional-changelog/conventional-changelog-writer/compare/<EMAIL>-changelog-writer@2.0.2) (2017-11-13)




**Note:** Version bump only for package conventional-changelog-writer

<a name="2.0.1"></a>
## [2.0.1](https://github.com/conventional-changelog/conventional-changelog-writer/compare/<EMAIL>-changelog-writer@2.0.1) (2017-09-01)

<a name="2.0.0"></a>
# 2.0.0 (2017-07-17)


### Bug Fixes

* **cli:** options format ([41c813b](https://github.com/conventional-changelog/conventional-changelog-writer/commit/41c813b))
* **cli:** require file with absolute path ([fe2b5fe](https://github.com/conventional-changelog/conventional-changelog-writer/commit/fe2b5fe)), closes [#13](https://github.com/conventional-changelog/conventional-changelog-writer/issues/13)
* **cli:** use absolute path to require context and options ([08808fe](https://github.com/conventional-changelog/conventional-changelog-writer/commit/08808fe))
* **cli:** when it is not tty, it should exit if errors ([aa8708c](https://github.com/conventional-changelog/conventional-changelog-writer/commit/aa8708c))
* **context:** auto link references if repoUrl ([d5d66f3](https://github.com/conventional-changelog/conventional-changelog-writer/commit/d5d66f3))
* **context.version:** only valid a semver can decide `context.isPatch` ([8dbc53a](https://github.com/conventional-changelog/conventional-changelog-writer/commit/8dbc53a))
* **date:** should use committerDate not authorDate ([fbdf73d](https://github.com/conventional-changelog/conventional-changelog-writer/commit/fbdf73d))
* **deps:** concat-stream should be in devdeps ([e90881c](https://github.com/conventional-changelog/conventional-changelog-writer/commit/e90881c))
* **deps:** require split2 ([59db605](https://github.com/conventional-changelog/conventional-changelog-writer/commit/59db605))
* **doFlush:** correct logic ([38e3c03](https://github.com/conventional-changelog/conventional-changelog-writer/commit/38e3c03)), closes [#19](https://github.com/conventional-changelog/conventional-changelog-writer/issues/19)
* **doFlush:** one it is the only potential release ([3d600cf](https://github.com/conventional-changelog/conventional-changelog-writer/commit/3d600cf))
* **err:** catch any possible error ([c934f50](https://github.com/conventional-changelog/conventional-changelog-writer/commit/c934f50))
* **error:** handle errors properly ([bde1200](https://github.com/conventional-changelog/conventional-changelog-writer/commit/bde1200))
* **firstRelease:** correct logic ([ccc02e1](https://github.com/conventional-changelog/conventional-changelog-writer/commit/ccc02e1))
* **functionify:** should not change falsy values ([1aed002](https://github.com/conventional-changelog/conventional-changelog-writer/commit/1aed002))
* **generateOn:** should pass the transformed commit ([2b6cc6c](https://github.com/conventional-changelog/conventional-changelog-writer/commit/2b6cc6c))
* **host:** auto removes "/" at the end of `options.host` ([2bdadf0](https://github.com/conventional-changelog/conventional-changelog-writer/commit/2bdadf0))
* **keyCommit:** all fields of `keyCommit` overwrites `context` ([63296b5](https://github.com/conventional-changelog/conventional-changelog-writer/commit/63296b5)), closes [#5](https://github.com/conventional-changelog/conventional-changelog-writer/issues/5)
* **linkReferences:** can be changed to `false` ([a56f9fd](https://github.com/conventional-changelog/conventional-changelog-writer/commit/a56f9fd))
* **notes:** do not include reverted notes ([4e60fe2](https://github.com/conventional-changelog/conventional-changelog-writer/commit/4e60fe2))
* **notesSort:** defaults to sort on `text` ([3511ffb](https://github.com/conventional-changelog/conventional-changelog-writer/commit/3511ffb))
* **options:** only apply default transform in certain conditions ([6080181](https://github.com/conventional-changelog/conventional-changelog-writer/commit/6080181))
* **partials:** only register if its a string ([915cbeb](https://github.com/conventional-changelog/conventional-changelog-writer/commit/915cbeb))
* **reverse:** should be the other way ([b4156e3](https://github.com/conventional-changelog/conventional-changelog-writer/commit/b4156e3))
* **template:** commit template markdown ([0949b5a](https://github.com/conventional-changelog/conventional-changelog-writer/commit/0949b5a))
* linting ([33ac525](https://github.com/conventional-changelog/conventional-changelog-writer/commit/33ac525))
* **template:** default commit template should handle unkown host ([d1ed4fc](https://github.com/conventional-changelog/conventional-changelog-writer/commit/d1ed4fc))
* **template:** remove an extra newline in footer ([f6180c5](https://github.com/conventional-changelog/conventional-changelog-writer/commit/f6180c5))
* **template:** should not html escape ([e4e33ae](https://github.com/conventional-changelog/conventional-changelog-writer/commit/e4e33ae))
* **template:** tweak ([ef6996a](https://github.com/conventional-changelog/conventional-changelog-writer/commit/ef6996a))
* **templates:** generate correct url if only host exists ([35f1799](https://github.com/conventional-changelog/conventional-changelog-writer/commit/35f1799))
* **templates:** incase partial is empty also don't ignore default partials ([d90fb65](https://github.com/conventional-changelog/conventional-changelog-writer/commit/d90fb65))
* **transform:** do not strip leading v ([8e2da57](https://github.com/conventional-changelog/conventional-changelog-writer/commit/8e2da57))
* **transform:** should work if any field is missing ([fd413ed](https://github.com/conventional-changelog/conventional-changelog-writer/commit/fd413ed))


### Features

* **defaults:** merge default options and make it less angular ([8e29f96](https://github.com/conventional-changelog/conventional-changelog-writer/commit/8e29f96)), closes [#3](https://github.com/conventional-changelog/conventional-changelog-writer/issues/3) [#4](https://github.com/conventional-changelog/conventional-changelog-writer/issues/4)
* migrate repo to lerna mono-repo ([793e823](https://github.com/conventional-changelog/conventional-changelog-writer/commit/793e823))
* **transform:** if returns a falsy value this commit is ignored ([9508ed6](https://github.com/conventional-changelog/conventional-changelog-writer/commit/9508ed6))
* use new api of `references` and `notes` ([4d27326](https://github.com/conventional-changelog/conventional-changelog-writer/commit/4d27326))
* **cli:** version can be passed directly as an input ([cadf7af](https://github.com/conventional-changelog/conventional-changelog-writer/commit/cadf7af))
* **commit:** `raw` object is attached to `commit` ([2ea9f04](https://github.com/conventional-changelog/conventional-changelog-writer/commit/2ea9f04))
* **commit.hbs:** scope can be missing ([82e0ffa](https://github.com/conventional-changelog/conventional-changelog-writer/commit/82e0ffa))
* **commit.hbs:** use `header` if `subject` is missing ([5e475a0](https://github.com/conventional-changelog/conventional-changelog-writer/commit/5e475a0))
* **compareFunc:** these values can be string or array ([464988c](https://github.com/conventional-changelog/conventional-changelog-writer/commit/464988c))
* **compareFunc:** use module "compare-func" ([520014e](https://github.com/conventional-changelog/conventional-changelog-writer/commit/520014e))
* **context:** expose `finalizeContext` to modify context at last ([d5545c0](https://github.com/conventional-changelog/conventional-changelog-writer/commit/d5545c0))
* **context:** fallback to repoUrl ([dc9c626](https://github.com/conventional-changelog/conventional-changelog-writer/commit/dc9c626))
* **context:** linkReferences has nothing to do with context.host ([1656df8](https://github.com/conventional-changelog/conventional-changelog-writer/commit/1656df8))
* **debug:** convient function for debugging ([c041e35](https://github.com/conventional-changelog/conventional-changelog-writer/commit/c041e35))
* **flush:** add `options.doFlush` to make it possible not to flush ([2fdf142](https://github.com/conventional-changelog/conventional-changelog-writer/commit/2fdf142))
* **generate:** originalCommits as last argument ([797fa8c](https://github.com/conventional-changelog/conventional-changelog-writer/commit/797fa8c))
* **generateOn:** also pass commits, context and options to the function ([a59c73c](https://github.com/conventional-changelog/conventional-changelog-writer/commit/a59c73c)), closes [ajoslin/conventional-changelog#135](https://github.com/ajoslin/conventional-changelog/issues/135)
* **generateOn:** by default if `commit.version` is a valid semver ([19ad3b1](https://github.com/conventional-changelog/conventional-changelog-writer/commit/19ad3b1))
* **generateOn:** if the commit is ignored fall back on the original ([be5723a](https://github.com/conventional-changelog/conventional-changelog-writer/commit/be5723a))
* **generateOn:** log doesn't have to be generated once ([ff88a62](https://github.com/conventional-changelog/conventional-changelog-writer/commit/ff88a62))
* **generateOn:** other type to disable ([9c50b90](https://github.com/conventional-changelog/conventional-changelog-writer/commit/9c50b90))
* **includeDetails:** return an object that contains more details ([81e79f7](https://github.com/conventional-changelog/conventional-changelog-writer/commit/81e79f7))
* **map:** change `options.replacements` to `options.map` ([d0a04ef](https://github.com/conventional-changelog/conventional-changelog-writer/commit/d0a04ef))
* **maxSubjectLength:** added ([83c98b9](https://github.com/conventional-changelog/conventional-changelog-writer/commit/83c98b9))
* **noteGroups:** remove and add note title transform ([abedbfd](https://github.com/conventional-changelog/conventional-changelog-writer/commit/abedbfd))
* **notes:** attach the commit to the note ([af89d4a](https://github.com/conventional-changelog/conventional-changelog-writer/commit/af89d4a)), closes [#12](https://github.com/conventional-changelog/conventional-changelog-writer/issues/12)
* **owner:** add owner context ([8d7b5d9](https://github.com/conventional-changelog/conventional-changelog-writer/commit/8d7b5d9)), closes [#7](https://github.com/conventional-changelog/conventional-changelog-writer/issues/7)
* **reverse:** new options for commits that are poured reversely ([613651e](https://github.com/conventional-changelog/conventional-changelog-writer/commit/613651e))
* **revert:** ignore reverted commits ([0f279ad](https://github.com/conventional-changelog/conventional-changelog-writer/commit/0f279ad))
* **transform:** add a transform option ([b05dc2e](https://github.com/conventional-changelog/conventional-changelog-writer/commit/b05dc2e)), closes [#2](https://github.com/conventional-changelog/conventional-changelog-writer/issues/2)
* **transform:** also pass context as an arg ([76b869d](https://github.com/conventional-changelog/conventional-changelog-writer/commit/76b869d))
* **version:** is not a required field any more ([3790d8f](https://github.com/conventional-changelog/conventional-changelog-writer/commit/3790d8f))
* **version:** strip leading v by default ([43c2c7e](https://github.com/conventional-changelog/conventional-changelog-writer/commit/43c2c7e))


### Performance Improvements

* **get/set:** drop dot-prop and just use lodash ([601e580](https://github.com/conventional-changelog/conventional-changelog-writer/commit/601e580))


### BREAKING CHANGES

* **context:** `context.host` cannot change the default of `context.linkReferences` because if the host is unknown, `context.host` is `undefined` and all links will just use `context.repository`.
* The upstream must use the new api of `references` and `notes`.

`closes` now becomes `references`
The `notes` object is no longer a key-value object but an array of note object, such as
```js
{
title: 'BREAKING AMEND',
text: 'some breaking change'
}
```
* **notes:** `includeDetails` will only include `log` and `keyCommit`.
* **templates:** If `partials` is not empty, it should not ignore header, commit and footer partials.
* **reverse:** when there is no commits left for the last block of logs it will still try to generate one. (Assume commits might be rebased or lost but still need a new version).
* **noteGroups:** `options.noteGroups` is no longer available. Filter the notes from upstream or in `options.transform` instead.
* **notes:** `notes` in `noteGroups` is not an array of simple string any more but object. You must use `note.text` to access the equivalent of previous `note`.
* **cli:** Previously version number has to be passed as a flag. As a version number is compulsory, it does not make sense for a flag to be compulsory. So if a version number is passed as an input it is still valid.
* **transform:** `options.hashLength`, `options.maxSubjectLength` and `options.map` are deprecated in favour of `options.transform`.
* **map:** `options.replacements` is now `options.map` and it can also take functions
* **compareFunc:** commitGroupsCompareFn -> commitGroupsSort, commitsCompareFn -> commitsSort, noteGroupsCompareFn -> noteGroupsSort and notesCompareFn -> notesSort
* **compareFunc:** Default compare functions no longer sort by lexicographical order. They use `localeCompare` instead
* **version:** `version` is not a required field and it is moved to the `context` object. If version is found in the last commit, it will be overwritten.

<a name="1.4.1"></a>
## [1.4.1](https://github.com/conventional-changelog/conventional-changelog-writer/compare/v1.4.0...v1.4.1) (2016-05-10)


### Bug Fixes

* **context:** auto link references if repoUrl([30bb234](https://github.com/conventional-changelog/conventional-changelog-writer/commit/30bb234))



<a name="1.4.0"></a>
# [1.4.0](https://github.com/conventional-changelog/conventional-changelog-writer/compare/v1.3.0...v1.4.0) (2016-05-10)


### Features

* **context:** fallback to repoUrl([e504682](https://github.com/conventional-changelog/conventional-changelog-writer/commit/e504682))



<a name="1.3.0"></a>
# [1.3.0](https://github.com/conventional-changelog/conventional-changelog-writer/compare/v1.2.1...v1.3.0) (2016-05-08)


### Features

* **debug:** convient function for debugging([3b6233f](https://github.com/conventional-changelog/conventional-changelog-writer/commit/3b6233f))



<a name="1.2.1"></a>
## [1.2.1](https://github.com/conventional-changelog/conventional-changelog-writer/compare/v1.2.0...v1.2.1) (2016-04-19)


### Bug Fixes

* **templates:** generate correct url if only host exists ([bda0328](https://github.com/conventional-changelog/conventional-changelog-writer/commit/bda0328))



<a name="1.2.0"></a>
# [1.2.0](https://github.com/conventional-changelog/conventional-changelog-writer/compare/v1.1.1...v1.2.0) (2016-04-17)


### Features

* **transform:** also pass context as an arg ([9bd984c](https://github.com/conventional-changelog/conventional-changelog-writer/commit/9bd984c))



<a name="1.1.1"></a>
## [1.1.1](https://github.com/stevemao/conventional-changelog-writer/compare/v1.1.0...v1.1.1) (2016-02-29)




<a name="1.1.0"></a>
# [1.1.0](https://github.com/stevemao/conventional-changelog-writer/compare/v1.0.3...v1.1.0) (2016-02-08)


### Features

* **generate:** originalCommits as last argument ([186bfb9](https://github.com/stevemao/conventional-changelog-writer/commit/186bfb9))



<a name="1.0.3"></a>
## [1.0.3](https://github.com/stevemao/conventional-changelog-writer/compare/v1.0.2...v1.0.3) (2016-02-06)


### Bug Fixes

* **firstRelease:** correct logic ([43552a2](https://github.com/stevemao/conventional-changelog-writer/commit/43552a2))



<a name="1.0.2"></a>
## [1.0.2](https://github.com/stevemao/conventional-changelog-writer/compare/v1.0.1...v1.0.2) (2016-02-06)


### Bug Fixes

* **doFlush:** one it is the only potential release ([cc3b5db](https://github.com/stevemao/conventional-changelog-writer/commit/cc3b5db))



<a name="1.0.1"></a>
## [1.0.1](https://github.com/stevemao/conventional-changelog-writer/compare/v1.0.0...v1.0.1) (2016-02-06)


### Bug Fixes

* **doFlush:** correct logic ([54d96cc](https://github.com/stevemao/conventional-changelog-writer/commit/54d96cc)), closes [#19](https://github.com/stevemao/conventional-changelog-writer/issues/19)



<a name="1.0.0"></a>
# [1.0.0](https://github.com/stevemao/conventional-changelog-writer/compare/v0.5.1...v1.0.0) (2016-02-05)




<a name="0.5.1"></a>
## [0.5.1](https://github.com/stevemao/conventional-changelog-writer/compare/v0.5.0...v0.5.1) (2016-02-03)


### Bug Fixes

* **context.version:** only valid a semver can decide `context.isPatch` ([59ed325](https://github.com/stevemao/conventional-changelog-writer/commit/59ed325))



<a name="0.5.0"></a>
# [0.5.0](https://github.com/stevemao/conventional-changelog-writer/compare/v0.4.2...v0.5.0) (2016-02-02)


### Features

* **flush:** add `options.doFlush` to make it possible not to flush ([7850589](https://github.com/stevemao/conventional-changelog-writer/commit/7850589))



<a name="0.4.2"></a>
## [0.4.2](https://github.com/stevemao/conventional-changelog-writer/compare/v0.4.1...v0.4.2) (2016-01-18)


### Features

* **generateOn:** also pass commits, context and options to the function ([3146f66](https://github.com/stevemao/conventional-changelog-writer/commit/3146f66)), closes [ajoslin/conventional-changelog#135](https://github.com/ajoslin/conventional-changelog/issues/135)



<a name="0.4.1"></a>
## [0.4.1](https://github.com/stevemao/conventional-changelog-writer/compare/v0.4.0...v0.4.1) (2015-09-30)


### Bug Fixes

* **template:** default commit template should handle unkown host ([ef62bfd](https://github.com/stevemao/conventional-changelog-writer/commit/ef62bfd))

### Features

* **context:** linkReferences has nothing to do with context.host ([f5883a6](https://github.com/stevemao/conventional-changelog-writer/commit/f5883a6))


### BREAKING CHANGES

* `context.host` cannot change the default of `context.linkReferences` because if the host is unknown, `context.host` is `undefined` and all links will just use `context.repository`.



<a name="0.4.0"></a>
# [0.4.0](https://github.com/stevemao/conventional-changelog-writer/compare/v0.3.2...v0.4.0) (2015-09-23)


### Bug Fixes

* **cli:** require file with absolute path ([e9d9702](https://github.com/stevemao/conventional-changelog-writer/commit/e9d9702)), closes [#13](https://github.com/stevemao/conventional-changelog-writer/issues/13)
* **notesSort:** defaults to sort on `text` ([6d3d564](https://github.com/stevemao/conventional-changelog-writer/commit/6d3d564))

### Features

* **notes:** attach the commit to the note ([2977336](https://github.com/stevemao/conventional-changelog-writer/commit/2977336)), closes [#12](https://github.com/stevemao/conventional-changelog-writer/issues/12)


### BREAKING CHANGES

* `notes` in `noteGroups` is not an array of simple string any more but object. You must use `note.text` to access the equivalent of previous `note`.
