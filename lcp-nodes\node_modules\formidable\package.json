{"name": "formidable", "version": "3.5.1", "license": "MIT", "description": "A node.js module for parsing form data, especially file uploads.", "homepage": "https://github.com/node-formidable/formidable", "funding": "https://ko-fi.com/tunnckoCore/commissions", "repository": "node-formidable/formidable", "type": "module", "main": "./dist/index.cjs", "exports": {".": {"import": {"default": "./src/index.js"}, "require": {"default": "./dist/index.cjs"}, "default": "./dist/index.cjs"}, "./src/helpers/*.js": {"import": {"default": "./src/helpers/*.js"}, "require": {"default": "./dist/helpers/*.cjs"}}, "./src/parsers/*.js": {"import": {"default": "./src/parsers/*.js"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["src", "dist"], "publishConfig": {"access": "public", "tag": "latest"}, "scripts": {"build-package": "rollup --config ./tool/rollup.config.js", "prepublishOnly": "npm run build-package", "bench": "node benchmark", "bench2prep": "node benchmark/server.js", "bench2": "bombardier --body-file=\"./README.md\" --method=POST --duration=10s --connections=100 http://localhost:3000/api/upload", "fmt": "yarn run fmt:prepare '**/*'", "fmt:prepare": "prettier --write", "lint": "yarn run lint:prepare .", "lint:prepare": "eslint --cache --fix --quiet --format codeframe", "reinstall": "del-cli ./node_modules ./yarn.lock", "postreinstall": "yarn setup", "setup": "yarn", "pretest": "del-cli ./test/tmp && make-dir ./test/tmp", "test-specific": "node --experimental-vm-modules ./node_modules/jest/bin/jest.js --testPathPattern=test/standalone/keep-alive-error.test.js", "test": "npm run test-jest && npm run test-node", "test-jest": "node --experimental-vm-modules ./node_modules/jest/bin/jest.js --testPathPattern=test/ --coverage", "test-node": "node --test test-node/", "pretest:ci": "yarn run pretest", "test:ci": "node --experimental-vm-modules node_modules/.bin/nyc jest --testPathPattern=test/ --coverage && node --experimental-vm-modules node_modules/.bin/nyc node --test test-node/"}, "dependencies": {"dezalgo": "^1.0.4", "hexoid": "^1.0.0", "once": "^1.4.0"}, "devDependencies": {"@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "@rollup/plugin-commonjs": "^25.0.2", "@rollup/plugin-node-resolve": "^15.1.0", "@sindresorhus/slugify": "^2.1.0", "@tunnckocore/prettier-config": "1.3.8", "del-cli": "3.0.0", "eslint": "6.8.0", "eslint-config-airbnb-base": "14.1.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-import": "2.20.2", "eslint-plugin-prettier": "3.1.3", "express": "4.17.1", "formdata-polyfill": "^4.0.10", "husky": "4.2.5", "jest": "27.2.4", "koa": "2.11.0", "lint-staged": "10.2.7", "make-dir-cli": "2.0.0", "nyc": "15.1.0", "prettier": "2.0.5", "prettier-plugin-pkgjson": "0.2.8", "rollup": "^3.25.3", "supertest": "6.1.6"}, "jest": {"verbose": true}, "husky": {"hooks": {"pre-commit": "git status --porcelain && yarn lint-staged", "commit-msg": "yarn commitlint -E HUSKY_GIT_PARAMS"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"!*.{js,jsx,ts,tsx}": ["yarn run fmt:prepare"], "*.{js,jsx,ts,tsx}": ["yarn run lint"]}, "renovate": {"extends": ["@tunnckocore", ":pinAllExceptPeerDependencies"]}, "packageManager": "yarn@1.22.17", "keywords": ["multipart", "form", "data", "querystring", "www", "json", "ulpoad", "file"]}