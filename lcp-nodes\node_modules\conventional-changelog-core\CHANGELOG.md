# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

### [4.2.4](https://www.github.com/conventional-changelog/conventional-changelog/compare/conventional-changelog-core-v4.2.3...conventional-changelog-core-v4.2.4) (2021-09-09)


### Bug Fixes

* **conventional-commits-parser:** address CVE-2021-23425 ([#841](https://www.github.com/conventional-changelog/conventional-changelog/issues/841)) ([02b3d53](https://www.github.com/conventional-changelog/conventional-changelog/commit/02b3d53a0c142f0c28ee7d190d210c76a62887c2))

### [4.2.3](https://www.github.com/conventional-changelog/conventional-changelog/compare/conventional-changelog-core-v4.2.2...conventional-changelog-core-v4.2.3) (2021-07-03)


### Bug Fixes

* **deps:** update dependency conventional-changelog-writer to v5 ([#731](https://www.github.com/conventional-changelog/conventional-changelog/issues/731)) ([b5951fb](https://www.github.com/conventional-changelog/conventional-changelog/commit/b5951fb5c58ada8d480d17213703d717acb1cd42))
* **deps:** update get-pkg-repo to ^4.0.0 ([#820](https://www.github.com/conventional-changelog/conventional-changelog/issues/820)) ([97bce29](https://www.github.com/conventional-changelog/conventional-changelog/commit/97bce29eda2494a05becd8b35bca851d36a4b403))

### [4.2.2](https://www.github.com/conventional-changelog/conventional-changelog/compare/conventional-changelog-core@4.2.1...v4.2.2) (2020-12-30)


### Bug Fixes

* **deps:** update dependency git-raw-commits to v2.0.8 ([#723](https://www.github.com/conventional-changelog/conventional-changelog/issues/723)) ([9682305](https://www.github.com/conventional-changelog/conventional-changelog/commit/968230536914a680237e830ccc8e125c56b0f0aa))

## [4.2.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@4.2.1) (2020-11-05)


### Bug Fixes

* **deps:** update dependency normalize-package-data to v3 ([#687](https://github.com/conventional-changelog/conventional-changelog/issues/687)) ([7b6ec0a](https://github.com/conventional-changelog/conventional-changelog/commit/7b6ec0add30915bc1569f82a007bb4d1d6df8e3e))
* **deps:** update dependency through2 to v4 ([#657](https://github.com/conventional-changelog/conventional-changelog/issues/657)) ([7ae618c](https://github.com/conventional-changelog/conventional-changelog/commit/7ae618c81491841e5b1d796d3933aac0c54bc312))





# [4.2.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@4.2.0) (2020-08-12)


### Features

* add support for '--skip-unstable' option ([#656](https://github.com/conventional-changelog/conventional-changelog/issues/656)) ([#656](https://github.com/conventional-changelog/conventional-changelog/issues/656)) ([0679d7a](https://github.com/conventional-changelog/conventional-changelog/commit/0679d7a1d7a8715918326f31ec3f6168c2341fd6))





## [4.1.8](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@4.1.8) (2020-06-20)

**Note:** Version bump only for package conventional-changelog-core





## [4.1.7](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@4.1.7) (2020-05-08)

**Note:** Version bump only for package conventional-changelog-core





## [4.1.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@4.1.6) (2020-05-08)


### Bug Fixes

* **conventional-changelog-core:** check if HEAD ref exists before using it ([#578](https://github.com/conventional-changelog/conventional-changelog/issues/578)) ([a49b19a](https://github.com/conventional-changelog/conventional-changelog/commit/a49b19a8c4b1d13559ebb02020d4f623189fae6a))
* **conventional-changelog-core:** fix duplicated commits when `from` is specified ([#573](https://github.com/conventional-changelog/conventional-changelog/issues/573)) ([287a801](https://github.com/conventional-changelog/conventional-changelog/commit/287a801ecde0a3856b6531cef53474d3a8b808b3)), closes [#567](https://github.com/conventional-changelog/conventional-changelog/issues/567)
* **conventional-changelog-core:** read current version properly when tagPrefix is provided ([#563](https://github.com/conventional-changelog/conventional-changelog/issues/563)) ([1deb63f](https://github.com/conventional-changelog/conventional-changelog/commit/1deb63fff9a07848c3964264c5ef4d082d654223)), closes [#562](https://github.com/conventional-changelog/conventional-changelog/issues/562) [#337](https://github.com/conventional-changelog/conventional-changelog/issues/337)





## [4.1.2](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@4.1.2) (2019-11-21)


### Bug Fixes

* call gitRawCommits with ranges [tag1..tag2, tag2..tag3, ..., tagX..HEAD] to make sure commits are returned in right order. ([2fba5c7](https://github.com/conventional-changelog/conventional-changelog/commit/2fba5c7a02e0e34093a6bd9a01109457db9b84c5)), closes [#408](https://github.com/conventional-changelog/conventional-changelog/issues/408)





## [4.1.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@4.1.1) (2019-11-14)

**Note:** Version bump only for package conventional-changelog-core





# [4.1.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@4.1.0) (2019-11-07)


### Features

* **conventional-changelog-core:** provide facility to define gitExecOpts. ([#480](https://github.com/conventional-changelog/conventional-changelog/issues/480)) ([814f878](https://github.com/conventional-changelog/conventional-changelog/commit/814f878054ca3c9ec00c3147478eb1e6a2762e9a))





## [4.0.3](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@4.0.3) (2019-10-24)


### Bug Fixes

* **deps:** update lodash to fix security issues ([#535](https://github.com/conventional-changelog/conventional-changelog/issues/535)) ([ac43f51](https://github.com/conventional-changelog/conventional-changelog/commit/ac43f51de1f3b597c32f7f8442917a2d06199018))





## [4.0.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@4.0.1) (2019-10-02)

**Note:** Version bump only for package conventional-changelog-core





# [4.0.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@4.0.0) (2019-07-29)


* refactor!: modify gitSemverTags to take options first (#390) ([dc8aeda](https://github.com/conventional-changelog/conventional-changelog/commit/dc8aeda)), closes [#390](https://github.com/conventional-changelog/conventional-changelog/issues/390)


### BREAKING CHANGES

* gitSemverTags now takes options followed by callback.





## [3.2.3](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.2.3) (2019-05-18)

**Note:** Version bump only for package conventional-changelog-core





## [3.2.2](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.2.2) (2019-04-11)

**Note:** Version bump only for package conventional-changelog-core





## [3.2.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.2.1) (2019-04-11)

**Note:** Version bump only for package conventional-changelog-core





# [3.2.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.2.0) (2019-04-10)


### Bug Fixes

* **deps:** update dependency through2 to v3 ([#392](https://github.com/conventional-changelog/conventional-changelog/issues/392)) ([26fe91f](https://github.com/conventional-changelog/conventional-changelog/commit/26fe91f))


### Features

* creating highly configurable preset, based on conventionalcommits.org ([#421](https://github.com/conventional-changelog/conventional-changelog/issues/421)) ([f2fb240](https://github.com/conventional-changelog/conventional-changelog/commit/f2fb240))





## [3.1.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.1.6) (2019-02-14)

**Note:** Version bump only for package conventional-changelog-core





## [3.1.5](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.1.5) (2018-11-01)

**Note:** Version bump only for package conventional-changelog-core





## [3.1.4](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.1.4) (2018-11-01)

**Note:** Version bump only for package conventional-changelog-core





## [3.1.3](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.1.3) (2018-11-01)


### Bug Fixes

* pin git-raw-commits until I have publication rights ([e41777c](https://github.com/conventional-changelog/conventional-changelog/commit/e41777c))





## [3.1.2](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.1.2) (2018-11-01)

**Note:** Version bump only for package conventional-changelog-core





## [3.1.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.1.1) (2018-11-01)


### Bug Fixes

* Upgrade to Lerna 3, fix Node.js v11 error ([#385](https://github.com/conventional-changelog/conventional-changelog/issues/385)) ([cdef282](https://github.com/conventional-changelog/conventional-changelog/commit/cdef282))





<a name="3.1.0"></a>
# [3.1.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.1.0) (2018-08-21)


### Features

* ability to reset changelog from scratch ([#350](https://github.com/conventional-changelog/conventional-changelog/issues/350)) ([0eea0af](https://github.com/conventional-changelog/conventional-changelog/commit/0eea0af))




<a name="3.0.0"></a>
# [3.0.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@3.0.0) (2018-05-29)


### Chores

* **package:** set Node requirement to oldest supported LTS ([#329](https://github.com/conventional-changelog/conventional-changelog/issues/329)) ([cae2fe0](https://github.com/conventional-changelog/conventional-changelog/commit/cae2fe0))


### BREAKING CHANGES

* **package:** Set the package's minimum required Node version to be the oldest LTS
currently supported by the Node Release working group. At this time,
that is Node 6 (which is in its Maintenance LTS phase).




<a name="2.0.11"></a>
## [2.0.11](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@2.0.11) (2018-04-16)


### Bug Fixes

* `tagPrefix` was not passed properly in conventional-changelog-core ([#300](https://github.com/conventional-changelog/conventional-changelog/issues/300)) ([be48f70](https://github.com/conventional-changelog/conventional-changelog/commit/be48f70))




<a name="2.0.10"></a>
## [2.0.10](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@2.0.10) (2018-03-28)




**Note:** Version bump only for package conventional-changelog-core

<a name="2.0.9"></a>
## [2.0.9](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@2.0.9) (2018-03-27)




**Note:** Version bump only for package conventional-changelog-core

<a name="2.0.8"></a>
## [2.0.8](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@2.0.8) (2018-03-27)




**Note:** Version bump only for package conventional-changelog-core

<a name="2.0.7"></a>
## [2.0.7](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@2.0.7) (2018-03-27)




**Note:** Version bump only for package conventional-changelog-core

<a name="2.0.6"></a>
## [2.0.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@2.0.6) (2018-03-22)




**Note:** Version bump only for package conventional-changelog-core

<a name="2.0.5"></a>
## [2.0.5](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@2.0.5) (2018-02-24)




**Note:** Version bump only for package conventional-changelog-core

<a name="2.0.4"></a>
## [2.0.4](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-core@2.0.4) (2018-02-20)




**Note:** Version bump only for package conventional-changelog-core

<a name="2.0.3"></a>
## [2.0.3](https://github.com/conventional-changelog/conventional-changelog-core/compare/<EMAIL>-changelog-core@2.0.3) (2018-02-13)




**Note:** Version bump only for package conventional-changelog-core

<a name="2.0.2"></a>
## [2.0.2](https://github.com/conventional-changelog/conventional-changelog-core/compare/<EMAIL>-changelog-core@2.0.2) (2018-02-13)




**Note:** Version bump only for package conventional-changelog-core

<a name="2.0.1"></a>
## [2.0.1](https://github.com/conventional-changelog/conventional-changelog-core/compare/<EMAIL>-changelog-core@2.0.1) (2018-02-05)




**Note:** Version bump only for package conventional-changelog-core

<a name="2.0.0"></a>
# [2.0.0](https://github.com/conventional-changelog/conventional-changelog-core/compare/<EMAIL>-changelog-core@2.0.0) (2018-01-29)


### Bug Fixes

* **writer:** normalize release headings ([#237](https://github.com/conventional-changelog/conventional-changelog-core/issues/237)) ([9e87dc3](https://github.com/conventional-changelog/conventional-changelog-core/commit/9e87dc3)), closes [/github.com/conventional-changelog/conventional-changelog/issues/214#issuecomment-326681934](https://github.com//github.com/conventional-changelog/conventional-changelog/issues/214/issues/issuecomment-326681934)


### BREAKING CHANGES

* **writer:** Logic for generating release headings has been changed to make all
heading levels the same (`##`/`h2`) for better compatibility with
screen readers and parsers, and to conform to HTML semantics. Patch
release titles are now wrapped in a `<small>` tag to maintain the
visual hierarchy of the previous style.

Fixes https://github.com/conventional-changelog/conventional-changelog/issues/214




<a name="1.9.5"></a>
## [1.9.5](https://github.com/conventional-changelog/conventional-changelog-core/compare/<EMAIL>-changelog-core@1.9.5) (2017-12-18)




**Note:** Version bump only for package conventional-changelog-core

<a name="1.9.4"></a>
## [1.9.4](https://github.com/conventional-changelog/conventional-changelog-core/compare/<EMAIL>-changelog-core@1.9.4) (2017-12-08)




**Note:** Version bump only for package conventional-changelog-core

<a name="1.9.3"></a>
## [1.9.3](https://github.com/conventional-changelog/conventional-changelog-core/compare/<EMAIL>-changelog-core@1.9.3) (2017-11-13)




**Note:** Version bump only for package conventional-changelog-core

<a name="1.9.2"></a>
## [1.9.2](https://github.com/conventional-changelog/conventional-changelog-core/compare/<EMAIL>-changelog-core@1.9.2) (2017-10-01)

<a name="1.9.1"></a>
## [1.9.1](https://github.com/conventional-changelog/conventional-changelog-core/compare/<EMAIL>-changelog-core@1.9.1) (2017-09-01)

<a name="1.9.0"></a>
# [1.9.0](https://github.com/conventional-changelog/conventional-changelog-core/compare/<EMAIL>-changelog-core@1.9.0) (2017-07-17)


### Features

* **context:** default currentTag may not prefix with v ([#179](https://github.com/conventional-changelog/conventional-changelog/issues/179)) ([431598a](https://github.com/conventional-changelog/conventional-changelog-core/commit/431598a))

<a name="1.8.0"></a>
# [1.8.0](https://github.com/conventional-changelog/conventional-changelog-core/compare/<EMAIL>-changelog-core@1.8.0) (2017-03-11)


### Features

* context.currentTag should take into account lerna tag format ([#178](https://github.com/conventional-changelog/conventional-changelog/issues/178)) ([f0e5875](https://github.com/conventional-changelog/conventional-changelog-core/commit/f0e5875))

<a name="1.5.0"></a>
# [1.5.0](https://github.com/conventional-changelog/conventional-changelog-core/compare/v1.4.0...v1.5.0) (2016-05-10)


### Features

* **context:** fallback to repoUrl([da0b096](https://github.com/conventional-changelog/conventional-changelog-core/commit/da0b096)), closes [#7](https://github.com/conventional-changelog/conventional-changelog-core/issues/7)



<a name="1.4.0"></a>
# [1.4.0](https://github.com/conventional-changelog/conventional-changelog-core/compare/v1.3.4...v1.4.0) (2016-05-08)


### Features

* **debug:** make options.debug as default writeOpts.debug([eeb7e8f](https://github.com/conventional-changelog/conventional-changelog-core/commit/eeb7e8f))



<a name="1.3.4"></a>
## [1.3.4](https://github.com/conventional-changelog/conventional-changelog-core/compare/v1.3.3...v1.3.4) (2016-05-07)


### Bug Fixes

* **mergeConfig:** respect issuePrefixes option ([4be052b](https://github.com/conventional-changelog/conventional-changelog-core/commit/4be052b)), closes [#6](https://github.com/conventional-changelog/conventional-changelog-core/issues/6) [#8](https://github.com/conventional-changelog/conventional-changelog-core/issues/8)



<a name="1.3.3"></a>
## [1.3.3](https://github.com/conventional-changelog/conventional-changelog-core/compare/v1.3.2...v1.3.3) (2016-04-19)


### Bug Fixes

* **unknownHost:** default context.repository ([eaa3b6f](https://github.com/conventional-changelog/conventional-changelog-core/commit/eaa3b6f))



<a name="1.3.2"></a>
## [1.3.2](https://github.com/conventional-changelog/conventional-changelog-core/compare/v1.3.1...v1.3.2) (2016-04-17)




<a name="1.3.1"></a>
## [1.3.1](https://github.com/conventional-changelog/conventional-changelog-core/compare/v1.3.0...v1.3.1) (2016-04-09)


### Bug Fixes

* **defaults:** context tags ([2571038](https://github.com/conventional-changelog/conventional-changelog-core/commit/2571038))



<a name="1.3.0"></a>
# [1.3.0](https://github.com/stevemao/conventional-changelog-core/compare/v1.2.0...v1.3.0) (2016-02-13)


### Features

* **debug:** add options.debug function ([aa56ae6](https://github.com/stevemao/conventional-changelog-core/commit/aa56ae6))



<a name="1.2.0"></a>
# [1.2.0](https://github.com/stevemao/conventional-changelog-core/compare/v1.1.0...v1.2.0) (2016-02-11)


### Features

* **merge:** ignore merge commits ([8f788dc](https://github.com/stevemao/conventional-changelog-core/commit/8f788dc))



<a name="1.1.0"></a>
# [1.1.0](https://github.com/stevemao/conventional-changelog-core/compare/v1.0.2...v1.1.0) (2016-02-08)


### Bug Fixes

* **default:** firstCommit and lastCommit should based on original unfiltered commits ([7fc49c9](https://github.com/stevemao/conventional-changelog-core/commit/7fc49c9)), closes [#2](https://github.com/stevemao/conventional-changelog-core/issues/2)



<a name="1.0.2"></a>
## [1.0.2](https://github.com/stevemao/conventional-changelog-core/compare/v1.0.1...v1.0.2) (2016-02-06)


### Bug Fixes

* **currentTag:** if unreleased, currentTag should be last commit hash ([e3d25ae](https://github.com/stevemao/conventional-changelog-core/commit/e3d25ae))



<a name="1.0.1"></a>
## [1.0.1](https://github.com/stevemao/conventional-changelog-core/compare/v1.0.0...v1.0.1) (2016-02-06)


### Bug Fixes

* **unreleased:** now it can output unreleased commits ([87b7340](https://github.com/stevemao/conventional-changelog-core/commit/87b7340))



<a name="1.0.0"></a>
# [1.0.0](https://github.com/stevemao/conventional-changelog-core/compare/v0.0.2...v1.0.0) (2016-02-05)


### Bug Fixes

* **oldNode:** git remote origin url feature is only available under node>=4 ([c69db53](https://github.com/stevemao/conventional-changelog-core/commit/c69db53))

### Features

* **pkg:** fallback to git remote origin url ([5b56952](https://github.com/stevemao/conventional-changelog-core/commit/5b56952))
* **unreleased:** option to output or not unreleased changelog ([9dfe8d8](https://github.com/stevemao/conventional-changelog-core/commit/9dfe8d8)), closes [ajoslin/conventional-changelog#120](https://github.com/ajoslin/conventional-changelog/issues/120)


### BREAKING CHANGES

* unreleased: If `context.version` is the same as the version of the last release, by default the unreleased chagnelog will not output.



<a name="0.0.2"></a>
## [0.0.2](https://github.com/stevemao/conventional-changelog-core/compare/v0.0.1...v0.0.2) (2016-01-30)


### Bug Fixes

* **error:** better error handling ([614ce1a](https://github.com/stevemao/conventional-changelog-core/commit/614ce1a)), closes [ajoslin/conventional-changelog#130](https://github.com/ajoslin/conventional-changelog/issues/130)



<a name="0.0.1"></a>
## 0.0.1 (2015-12-26)


### Features

* **config:** change preset to config ([85fd9d9](https://github.com/stevemao/conventional-changelog-core/commit/85fd9d9))
* **init:** extract core from conventional-changelog ([4a4bca3](https://github.com/stevemao/conventional-changelog-core/commit/4a4bca3))


### BREAKING CHANGES

* config: `options.preset` is removed in favour of `options.config`
