// Type definitions for Node.js 10.17
// Project: http://nodejs.org/
// Definitions by: Microsoft TypeScript <https://github.com/Microsoft>
//                 DefinitelyTyped <https://github.com/DefinitelyTyped>
//                 <PERSON> <https://github.com/jkomyno>
//                 <PERSON><PERSON> <https://github.com/alvis>
//                 <PERSON> <https://github.com/r3nya>
//                 Chigozirim C. <https://github.com/smac89>
//                 Deividas Bakanas <https://github.com/DeividasBakanas>
//                 Eugene <PERSON>. <PERSON>. <PERSON> <https://github.com/eyqs>
//                 <PERSON><PERSON> <https://github.com/<PERSON><PERSON>-<PERSON>-<PERSON>>
//                 Hoàng <PERSON>ăn <PERSON> <https://github.com/KSXGitHub>
//                 Huw <https://github.com/hoo29>
//                 <PERSON><PERSON> <https://github.com/kjin>
//                 <PERSON> <https://github.com/ajafff>
//                 Lishude <https://github.com/islishude>
//                 <PERSON><PERSON> <https://github.com/mwiktorczyk>
//                 Mohsen Azimi <https://github.com/mohsen1>
//                 Nicolas Even <https://github.com/n-e>
//                 Nikita Galkin <https://github.com/galkin>
//                 Parambir Singh <https://github.com/parambirs>
//                 Sebastian Silbermann <https://github.com/eps1lon>
//                 Simon Schick <https://github.com/SimonSchick>
//                 Thomas den Hollander <https://github.com/ThomasdenH>
//                 Wilco Bakker <https://github.com/WilcoBakker>
//                 wwwy3y3 <https://github.com/wwwy3y3>
//                 Zane Hannan AU <https://github.com/ZaneHannanAU>
//                 Jeremie Rodriguez <https://github.com/jeremiergz>
//                 Samuel Ainsworth <https://github.com/samuela>
//                 Kyle Uehlein <https://github.com/kuehlein>
//                 Thanik Bhongbhibhat <https://github.com/bhongy>
//                 Minh Son Nguyen <https://github.com/nguymin4>
//                 ExE Boss <https://github.com/ExE-Boss>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped

// NOTE: These definitions support NodeJS and TypeScript 3.7.
// This isn't strictly needed since 3.7 has the assert module, but this way we're consistent.
// Typically type modificatons should be made in base.d.ts instead of here

/// <reference path="base.d.ts" />
/// <reference path="ts3.6/base.d.ts" />
