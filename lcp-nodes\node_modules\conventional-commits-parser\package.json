{"name": "conventional-commits-parser", "version": "3.2.4", "description": "Parse raw conventional commits", "bugs": {"url": "https://github.com/conventional-changelog/conventional-changelog/issues"}, "homepage": "https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-commits-parser#readme", "author": {"name": "<PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com", "url": "https://github.com/stevemao"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/conventional-changelog.git"}, "license": "MIT", "engines": {"node": ">=10"}, "files": ["index.js", "cli.js", "lib"], "keywords": ["conventional-commits-parser", "changelog", "conventional", "parser", "parsing", "logs"], "dependencies": {"is-text-path": "^1.0.1", "JSONStream": "^1.0.4", "lodash": "^4.17.15", "meow": "^8.0.0", "split2": "^3.0.0", "through2": "^4.0.0"}, "scripts": {"test-windows": "echo 'make work on windows'"}, "bin": {"conventional-commits-parser": "cli.js"}, "devDependencies": {"forceable-tty": "^0.1.0"}}