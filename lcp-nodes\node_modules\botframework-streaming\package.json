{"name": "botframework-streaming", "author": "Microsoft Corp.", "description": "Streaming library for the Microsoft Bot Framework", "version": "4.22.3", "license": "MIT", "keywords": ["botbuilder", "botframework", "bots", "chatbots", "websockets", "streaming"], "bugs": {"url": "https://github.com/microsoft/botbuilder-js/issues"}, "repository": {"type": "git", "url": "https://github.com/microsoft/botbuilder-js.git"}, "main": "lib/index.js", "browser": "lib/index-browser.js", "types": "lib/index.d.ts", "typesVersions": {"<3.9": {"*": ["_ts3.4/*"]}}, "dependencies": {"@types/node": "^10.17.27", "@types/ws": "^6.0.3", "uuid": "^8.3.2", "ws": "^7.5.10"}, "devDependencies": {"chai": "^4.2.0"}, "scripts": {"build": "npm-run-all -p build:lib build:es5", "build:es5": "npm-run-all build:es5:tsc build:es5:browserify", "build:es5:browserify": "browserify -s BFSE --debug es5/index-browser.js | exorcist lib/index-browser.js.map > lib/index-browser.js", "build:es5:tsc": "tsc -p tsconfig-es5.json", "build:lib": "tsc -p tsconfig.json", "build:rollup": "yarn clean && yarn build && api-extractor run --verbose --local", "clean": "rimraf _ts3.4 es5 lib", "depcheck": "depcheck --config ../../.depcheckrc", "lint": "eslint . --ext .js,.ts", "postbuild": "downlevel-dts lib _ts3.4/lib --checksum", "test": "npm-run-all build test:mocha", "test:compat": "api-extractor run --verbose", "test:mocha": "nyc mocha tests"}, "mocha": {"checkLeaks": true, "exit": true}, "files": ["_ts3.4", "lib", "src"]}