# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.0.9](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@2.0.9) (2020-11-05)

**Note:** Version bump only for package conventional-changelog-ember





## [2.0.8](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@2.0.8) (2020-05-08)

**Note:** Version bump only for package conventional-changelog-ember





## [2.0.3](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@2.0.3) (2019-10-02)


### Bug Fixes

* use full commit hash in commit link ([7a60dec](https://github.com/conventional-changelog/conventional-changelog/commit/7a60dec)), closes [#476](https://github.com/conventional-changelog/conventional-changelog/issues/476)





## [2.0.2](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@2.0.2) (2018-11-01)


### Bug Fixes

* Upgrade to Lerna 3, fix Node.js v11 error ([#385](https://github.com/conventional-changelog/conventional-changelog/issues/385)) ([cdef282](https://github.com/conventional-changelog/conventional-changelog/commit/cdef282))





<a name="2.0.1"></a>
## [2.0.1](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@2.0.1) (2018-08-21)




**Note:** Version bump only for package conventional-changelog-ember

<a name="2.0.0"></a>
# [2.0.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@2.0.0) (2018-06-06)


### Features

* re-use parser options within each preset ([#335](https://github.com/conventional-changelog/conventional-changelog/issues/335)) ([d3eaacf](https://github.com/conventional-changelog/conventional-changelog/commit/d3eaacf)), closes [#241](https://github.com/conventional-changelog/conventional-changelog/issues/241)


### BREAKING CHANGES

* Re-use parser options object between components of a preset. For some
presets this may change the behavior of `conventional-recommended-bump`
as the parser options object for the `conventional-recommended-bump` options
within a preset were different than the parser options object for the
`conventional-changelog` options within a preset.

If you are not using `conventional-recommended-bump`, then this is
**not** a breaking change for you.




<a name="1.0.0"></a>
# [1.0.0](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@1.0.0) (2018-05-29)


### Chores

* **package:** set Node requirement to oldest supported LTS ([#329](https://github.com/conventional-changelog/conventional-changelog/issues/329)) ([cae2fe0](https://github.com/conventional-changelog/conventional-changelog/commit/cae2fe0))


### Code Refactoring

* remove anchor from header templates ([#301](https://github.com/conventional-changelog/conventional-changelog/issues/301)) ([346f24f](https://github.com/conventional-changelog/conventional-changelog/commit/346f24f)), closes [#186](https://github.com/conventional-changelog/conventional-changelog/issues/186)


### BREAKING CHANGES

* **package:** Set the package's minimum required Node version to be the oldest LTS
currently supported by the Node Release working group. At this time,
that is Node 6 (which is in its Maintenance LTS phase).
* Anchor tags are removed from the changelog header templates. The
rendered Markdown will no longer contain anchor tags proceeding the
version number header that constitutes the changelog header. This means
that consumers of rendered markdown will not be able to use a URL that
has been constructed to contain a version number anchor tag reference,
since the anchor tag won't exist in the rendered markdown.

It's stronly recomended consumers use the full URL path to the release
page for a given version, as that URL is a permalink to that verison,
contains all relavent release information, and does not, otherwise, rely
on the anchor tag being excessible from the current page view.

As an example, for version `2.0.0` of a GitHub project, the following
URL should be used:
- https://github.com/conventional-changelog/releaser-tools/releases/tag/v2.0.0




<a name="0.3.12"></a>
## [0.3.12](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@0.3.12) (2018-04-16)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.3.11"></a>
## [0.3.11](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@0.3.11) (2018-03-28)


### Bug Fixes

* revert previous change ([2f4530f](https://github.com/conventional-changelog/conventional-changelog/commit/2f4530f))




<a name="0.3.10"></a>
## [0.3.10](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@0.3.10) (2018-03-27)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.3.9"></a>
## [0.3.9](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@0.3.9) (2018-03-27)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.3.8"></a>
## [0.3.8](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@0.3.8) (2018-03-27)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.3.7"></a>
## [0.3.7](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@0.3.7) (2018-03-22)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.3.6"></a>
## [0.3.6](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@0.3.6) (2018-02-24)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.3.5"></a>
## [0.3.5](https://github.com/conventional-changelog/conventional-changelog/compare/<EMAIL>-changelog-ember@0.3.5) (2018-02-20)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.3.4"></a>
## [0.3.4](https://github.com/stevemao/conventional-changelog-ember/compare/<EMAIL>-changelog-ember@0.3.4) (2018-02-13)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.3.3"></a>
## [0.3.3](https://github.com/stevemao/conventional-changelog-ember/compare/<EMAIL>-changelog-ember@0.3.3) (2018-02-13)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.3.2"></a>
## [0.3.2](https://github.com/stevemao/conventional-changelog-ember/compare/<EMAIL>-changelog-ember@0.3.2) (2018-02-05)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.3.1"></a>
## [0.3.1](https://github.com/stevemao/conventional-changelog-ember/compare/<EMAIL>-changelog-ember@0.3.1) (2018-01-29)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.3.0"></a>
# [0.3.0](https://github.com/stevemao/conventional-changelog-ember/compare/<EMAIL>-changelog-ember@0.3.0) (2017-12-18)


### Features

* **preset:** add recommended-bump opts into presets ([60815b5](https://github.com/stevemao/conventional-changelog-ember/commit/60815b5)), closes [#241](https://github.com/stevemao/conventional-changelog-ember/issues/241)




<a name="0.2.10"></a>
## [0.2.10](https://github.com/stevemao/conventional-changelog-ember/compare/<EMAIL>-changelog-ember@0.2.10) (2017-12-08)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.2.9"></a>
## [0.2.9](https://github.com/stevemao/conventional-changelog-ember/compare/<EMAIL>-changelog-ember@0.2.9) (2017-11-13)




**Note:** Version bump only for package conventional-changelog-ember

<a name="0.2.8"></a>
## [0.2.8](https://github.com/stevemao/conventional-changelog-ember/compare/<EMAIL>-changelog-ember@0.2.8) (2017-10-01)

<a name="0.2.7"></a>
## [0.2.7](https://github.com/stevemao/conventional-changelog-ember/compare/<EMAIL>-changelog-ember@0.2.7) (2017-09-01)

<a name="0.2.6"></a>
## [0.2.6](https://github.com/stevemao/conventional-changelog-ember/compare/<EMAIL>-changelog-ember@0.2.6) (2017-07-17)

<a name="0.2.5"></a>
## [0.2.5](https://github.com/stevemao/conventional-changelog-ember/compare/<EMAIL>-changelog-ember@0.2.5) (2017-03-11)
