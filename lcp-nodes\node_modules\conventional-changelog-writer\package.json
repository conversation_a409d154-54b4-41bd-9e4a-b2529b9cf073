{"name": "conventional-changelog-writer", "version": "5.0.1", "description": "Write logs based on conventional commits and templates", "bugs": {"url": "https://github.com/conventional-changelog/conventional-changelog/issues"}, "homepage": "https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-writer#readme", "author": {"name": "<PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com", "url": "https://github.com/stevemao"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/conventional-changelog.git"}, "license": "MIT", "engines": {"node": ">=10"}, "files": ["index.js", "cli.js", "lib", "templates"], "keywords": ["conventional-changelog-writer", "changelog", "conventional", "commits", "templates", "writer", "writing", "logs"], "dependencies": {"conventional-commits-filter": "^2.0.7", "dateformat": "^3.0.0", "handlebars": "^4.7.7", "json-stringify-safe": "^5.0.1", "lodash": "^4.17.15", "meow": "^8.0.0", "semver": "^6.0.0", "split": "^1.0.0", "through2": "^4.0.0"}, "scripts": {"test-windows": "echo 'make work on windows'"}, "bin": {"conventional-changelog-writer": "cli.js"}, "devDependencies": {"dedent": "^0.7.0", "forceable-tty": "^0.1.0"}}